package entities;

import components.Position;
import components.SpriteComponent;
import components.MovementComponent.Direction;
import ai.AIBehavior;
import tileengine.Tileset;

/**
 * Base ghost class with AI behavior interface.
 * Provides common functionality for all ghost types.
 * <p>
 * This class provides:
 * - AI behavior management and updates
 * - Ghost state management (normal, frightened, eaten)
 * - Target setting and pathfinding integration
 * - Power pellet response (becoming frightened)
 * - Respawn and home base mechanics
 */
public abstract class Ghost extends Entity {
    // Ghost states
    public enum GhostState {
        NORMAL,     // Regular AI behavior
        FRIGHTENED, // Running away from player (vulnerable)
        EATEN,      // Returning to base after being eaten
        SPAWNING    // Just spawned, entering play area
    }

    // AI and behavior
    protected AIBehavior aiBehavior;
    protected GhostState currentState;
    protected Entity targetEntity; // Usually the player
    protected Position homeBase;   // Spawn/respawn location

    // Movement and timing
    protected double normalSpeed;
    protected double frightenedSpeed;
    protected double eatenSpeed;

    // State timers
    protected double frightenedTimeRemaining;
    protected double stateChangeTime;

    // Ghost properties
    protected GhostColor ghostColor;

    /**
     * Enumeration of ghost colors for identification and sprites.
     */
    public enum GhostColor {
        RED,
        PINK,
        BLUE,
        ORANGE
    }

    /**
     * Creates a new Ghost with specified AI behavior.
     *
     * @param startPosition Starting position (usually home base)
     * @param ghostColor    Color/type of this ghost
     * @param aiBehavior    AI behavior implementation
     * @param normalSpeed   Normal movement speed
     */
    public Ghost(Position startPosition, GhostColor ghostColor,
                 AIBehavior aiBehavior, double normalSpeed) {
        super(startPosition, createGhostSprite(ghostColor), EntityType.GHOST);

        // TODO: Initialize ghost
        // - Set this.ghostColor = ghostColor
        // - Set this.aiBehavior = aiBehavior
        // - Set this.normalSpeed = normalSpeed
        // - Set frightenedSpeed = normalSpeed * 0.5 (slower when frightened)
        // - Set eatenSpeed = normalSpeed * 2.0 (faster when returning to base)
        // - Set currentState = GhostState.SPAWNING
        // - Set homeBase = copy of startPosition
        // - Enable movement with normalSpeed
        // - Initialize timers to 0
        throw new UnsupportedOperationException("Ghost constructor implementation needed");
    }

    /**
     * Creates the sprite component for the specified ghost color.
     *
     * @param color The color of the ghost
     * @return SpriteComponent configured for the ghost
     */
    private static SpriteComponent createGhostSprite(GhostColor color) {
        // TODO: Create ghost sprite
        // - Switch on color to select appropriate sprite:
        //   - RED: Use red ghost sprites
        //   - PINK: Use pink ghost sprites  
        //   - BLUE: Use blue ghost sprites
        //   - ORANGE: Use orange ghost sprites
        // - For now, use Tileset.ENEMY as placeholder
        // - Set up directional animations if available
        // - Return configured SpriteComponent
        throw new UnsupportedOperationException("Ghost sprite creation implementation needed");
    }

    /**
     * Updates the ghost AI and behavior.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    @Override
    protected void updateEntity(double deltaTime) {
        // TODO: Update ghost logic
        // - If not isActive, return early
        // - Update state timers (frightened timer, etc.)
        // - Update AI behavior based on current state
        // - Handle state transitions (frightened -> normal, etc.)
        // - Update movement speed based on current state
        // - Call AI behavior update
        throw new UnsupportedOperationException("Ghost update implementation needed");
    }

    /**
     * Updates the AI behavior system.
     *
     * @param deltaTime Time elapsed since last update
     */
    public void updateAI(double deltaTime) {
        // TODO: Update AI behavior
        // - If aiBehavior is null, return early
        // - Set AI behavior target (usually player position)
        // - Call aiBehavior.update() with current state
        // - Get next move from AI behavior
        // - Apply movement using movement component
        throw new UnsupportedOperationException("AI update implementation needed");
    }

    /**
     * Sets the target entity for AI behavior (usually the player).
     *
     * @param target The entity to target
     */
    public void setTarget(Entity target) {
        // TODO: Set AI target
        // - Set this.targetEntity = target
        // - If aiBehavior exists, update its target
        // - Reset any target-dependent AI state
        throw new UnsupportedOperationException("Target setting implementation needed");
    }

    /**
     * Gets the current AI behavior implementation.
     *
     * @return The current AI behavior
     */
    public AIBehavior getBehavior() {
        return aiBehavior;
    }

    /**
     * Sets a new AI behavior for this ghost.
     *
     * @param newBehavior The new AI behavior to use
     */
    public void setBehavior(AIBehavior newBehavior) {
        // TODO: Change AI behavior
        // - Set this.aiBehavior = newBehavior
        // - Initialize new behavior with current state
        // - Transfer relevant state information
        throw new UnsupportedOperationException("Behavior change implementation needed");
    }

    /**
     * Handles collision with other entities.
     *
     * @param other The entity this ghost collided with
     */
    @Override
    protected void handleCollision(Entity other) {
        // TODO: Handle ghost collisions
        // - If other is PLAYER:
        //   - If currentState == FRIGHTENED: ghost gets eaten
        //   - If currentState == NORMAL: player loses life
        //   - If currentState == EATEN: no collision
        // - Handle other collision types as needed
        throw new UnsupportedOperationException("Ghost collision handling implementation needed");
    }

    /**
     * Transitions to frightened state when player gets power pellet.
     *
     * @param duration Duration of frightened state in seconds
     */
    public void becomeFrightened(double duration) {
        // TODO: Enter frightened state
        // - If currentState == EATEN, don't change state
        // - Set currentState = GhostState.FRIGHTENED
        // - Set frightenedTimeRemaining = duration
        // - Update movement speed to frightenedSpeed
        // - Change sprite to frightened appearance
        // - Reverse direction to start fleeing
        // - Update AI behavior for fleeing
        throw new UnsupportedOperationException("Frightened state implementation needed");
    }

    /**
     * Handles being eaten by powered-up player.
     */
    public void getEaten() {
        // TODO: Handle being eaten
        // - Set currentState = GhostState.EATEN
        // - Change sprite to "eyes only" appearance
        // - Set movement speed to eatenSpeed
        // - Set target to homeBase position
        // - Update AI to pathfind directly to base
        // - Trigger eaten sound/score effects
        throw new UnsupportedOperationException("Eaten state implementation needed");
    }

    /**
     * Returns the ghost to normal state after being eaten or frightened.
     */
    public void returnToNormal() {
        // TODO: Return to normal state
        // - Set currentState = GhostState.NORMAL
        // - Reset sprite to normal appearance
        // - Set movement speed to normalSpeed
        // - Reset AI behavior to normal pattern
        // - Clear state timers
        throw new UnsupportedOperationException("Normal state return implementation needed");
    }

    /**
     * Respawns the ghost at its home base.
     */
    public void respawn() {
        // TODO: Respawn ghost
        // - Teleport to homeBase position
        // - Set currentState = GhostState.SPAWNING
        // - Reset all timers
        // - Set movement speed to normalSpeed
        // - Reset sprite and AI behavior
        // - Set brief spawn invulnerability if needed
        throw new UnsupportedOperationException("Ghost respawn implementation needed");
    }

    /**
     * Updates state timers and handles state transitions.
     *
     * @param deltaTime Time elapsed since last update
     */
    private void updateStateTimers(double deltaTime) {
        // TODO: Update timers and state transitions
        // - If in FRIGHTENED state:
        //   - Decrease frightenedTimeRemaining
        //   - If timer expires, return to normal
        //   - Handle blinking warning near expiration
        // - If in EATEN state:
        //   - Check if reached home base
        //   - If at base, start respawn sequence
        // - If in SPAWNING state:
        //   - Wait for spawn delay, then enter normal state
        throw new UnsupportedOperationException("State timer update implementation needed");
    }

    /**
     * Checks if the ghost can currently harm the player.
     *
     * @return true if ghost is dangerous to player
     */
    public boolean isDangerous() {
        // TODO: Check danger state
        // - Return true for NORMAL state
        // - Return false for FRIGHTENED, EATEN, SPAWNING states
        throw new UnsupportedOperationException("Danger check implementation needed");
    }

    /**
     * Checks if the ghost can be eaten by the player.
     *
     * @return true if ghost is vulnerable
     */
    public boolean isVulnerable() {
        // TODO: Check vulnerable state
        // - Return true only for FRIGHTENED state
        // - Return false for all other states
        throw new UnsupportedOperationException("Vulnerability check implementation needed");
    }

    /**
     * Gets the ghost's home base position.
     *
     * @return The home base position
     */
    public Position getHomeBase() {
        return homeBase;
    }

    /**
     * Sets a new home base position.
     *
     * @param newHomeBase The new home base position
     */
    public void setHomeBase(Position newHomeBase) {
        // TODO: Set new home base
        // - Set this.homeBase = copy of newHomeBase
        // - Update AI behavior with new base location if needed
        throw new UnsupportedOperationException("Home base setting implementation needed");
    }

    /**
     * Gets the current ghost state.
     *
     * @return The current state
     */
    public GhostState getCurrentState() {
        return currentState;
    }

    /**
     * Gets the ghost's color.
     *
     * @return The ghost color
     */
    public GhostColor getGhostColor() {
        return ghostColor;
    }

    /**
     * Gets the time remaining in frightened state.
     *
     * @return Frightened time remaining in seconds, or 0 if not frightened
     */
    public double getFrightenedTimeRemaining() {
        return currentState == GhostState.FRIGHTENED ? frightenedTimeRemaining : 0.0;
    }

    /**
     * Gets the current target entity.
     *
     * @return The target entity, or null if no target
     */
    public Entity getTarget() {
        return targetEntity;
    }

    @Override
    public String toString() {
        return String.format("Ghost[id=%s, color=%s, state=%s, pos=%s]",
                getEntityId(), ghostColor, currentState, getPosition());
    }
}