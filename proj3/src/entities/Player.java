package entities;

import components.MovementComponent.Direction;
import components.Position;
import components.SpriteComponent;
import config.GameConfig;
import edu.princeton.cs.algs4.StdDraw;
import managers.ScoreManager;
import managers.SkillManager;

/**
 * Player entity with input handling and skill management.
 * Represents the player-controlled Pac-Man character.
 * <p>
 * This class provides:
 * - Keyboard input processing for movement
 * - Skill activation and management
 * - Pellet collection and scoring
 * - Player state management (alive, powered up, etc.)
 * - Integration with game systems
 */
public class Player extends Entity {
    // Player state
    private boolean isAlive;
    private int lives;
    private boolean isPoweredUp;
    private double powerUpTimeRemaining;

    // Input handling
    private Direction pendingDirection;
    private boolean[] keysPressed;

    // Skills and abilities
    private SkillManager skillManager;
    private boolean canUseSkills;

    // Scoring
    private ScoreManager scoreManager;

    // Animation and visual state
    private double lastInputTime;
    private boolean isEating; // For eating animation

    // Configuration
    private static final double PLAYER_SPEED = GameConfig.PLAYER_SPEED;
    private static final double POWER_UP_DURATION = GameConfig.POWER_PELLET_DURATION;
    private static final int STARTING_LIVES = GameConfig.STARTING_LIVES;

    /**
     * Creates a new Player entity at the specified position.
     *
     * @param startPosition Starting position in the world
     * @param skillManager  Reference to the skill management system
     * @param scoreManager  Reference to the scoring system
     */
    public Player(Position startPosition, SkillManager skillManager, ScoreManager scoreManager) {
        super(startPosition, createPlayerSprite(), EntityType.PLAYER);

        // Set this.skillManager = skillManager
        this.skillManager = skillManager;
        // Set this.scoreManager = scoreManager
        this.scoreManager = scoreManager;
        // Set isAlive = true, lives = STARTING_LIVES
        this.isAlive = true;
        this.lives = STARTING_LIVES;
        // Set isPoweredUp = false, powerUpTimeRemaining = 0
        this.isPoweredUp = false;
        this.powerUpTimeRemaining = 0.0;
        // Initialize keysPressed array for WASD keys (W, A, S, D)
        this.keysPressed = new boolean[4]; // [W, A, S, D]
        // Set pendingDirection = Direction.NONE
        this.pendingDirection = Direction.NONE;
        // Enable movement with PLAYER_SPEED
        enableMovement(PLAYER_SPEED);
        // Set canUseSkills = true
        this.canUseSkills = true;
        // Initialize timing variables
        this.lastInputTime = 0.0;
        this.isEating = false;
    }

    /**
     * Creates the sprite component for the player.
     * Uses the existing avatar animation frames.
     *
     * @return SpriteComponent configured for player animation
     */
    private static SpriteComponent createPlayerSprite() {
        // Use Tileset.AVATAR_0, AVATAR_1, AVATAR_2, AVATAR_3 for animation frames
        tileengine.TETile[] animationFrames = {
                tileengine.Tileset.AVATAR_0,
                tileengine.Tileset.AVATAR_1,
                tileengine.Tileset.AVATAR_2,
                tileengine.Tileset.AVATAR_3
        };

        // Set appropriate frame time for smooth animation (0.2 seconds per frame)
        double frameTime = 0.2;

        // Return configured SpriteComponent
        return new SpriteComponent(animationFrames, frameTime);
    }

    /**
     * Updates the player entity including input processing and state management.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    @Override
    protected void updateEntity(double deltaTime) {
        // If not isAlive, return early
        if (!isAlive) {
            return;
        }

        // Process keyboard input with handleInput()
        handleInput();

        // Update power-up timer if isPoweredUp
        if (isPoweredUp) {
            updatePowerUp(deltaTime);
        }

        // Update movement direction based on input
        tryMovePendingDirection();

        // Update eating animation state (clear after some time)
        if (isEating) {
            // Clear eating state after a short duration
            // This could be enhanced with a timer if needed
            isEating = false;
        }

        // Check for pellet collection at current position would be handled
        // by collision detection in the EntityManager
    }

    /**
     * Handles keyboard input for player movement and actions.
     */
    private void handleInput() {
        // Check for WASD key presses using StdDraw
        boolean wPressed = StdDraw.isKeyPressed('W') || StdDraw.isKeyPressed('w');
        boolean aPressed = StdDraw.isKeyPressed('A') || StdDraw.isKeyPressed('a');
        boolean sPressed = StdDraw.isKeyPressed('S') || StdDraw.isKeyPressed('s');
        boolean dPressed = StdDraw.isKeyPressed('D') || StdDraw.isKeyPressed('d');

        // Update keysPressed array to track held keys [W, A, S, D]
        keysPressed[0] = wPressed;
        keysPressed[1] = aPressed;
        keysPressed[2] = sPressed;
        keysPressed[3] = dPressed;

        // Set pendingDirection based on most recent key press
        if (wPressed) {
            pendingDirection = Direction.UP;
            lastInputTime = System.currentTimeMillis() / 1000.0;
        } else if (sPressed) {
            pendingDirection = Direction.DOWN;
            lastInputTime = System.currentTimeMillis() / 1000.0;
        } else if (aPressed) {
            pendingDirection = Direction.LEFT;
            lastInputTime = System.currentTimeMillis() / 1000.0;
        } else if (dPressed) {
            pendingDirection = Direction.RIGHT;
            lastInputTime = System.currentTimeMillis() / 1000.0;
        }

        // Handle skill activation keys (space, enter, etc.)
        if (canUseSkills && (StdDraw.isKeyPressed(' ') || StdDraw.isKeyPressed('\n'))) {
            // Activate power pellet skill if available
            if (skillManager != null) {
                skillManager.activateSkill("power_pellet", this);
            }
        }
    }

    /**
     * Attempts to move in the pending direction.
     * Called after input processing to apply movement.
     */
    private void tryMovePendingDirection() {
        // If pendingDirection is NONE, return
        if (pendingDirection == Direction.NONE) {
            return;
        }

        // Use movement component to try changing direction
        if (movement != null) {
            // Try to change direction - MovementComponent will handle collision checking
            movement.setDirection(pendingDirection, PLAYER_SPEED);
            // If direction change successful, clear pendingDirection
            pendingDirection = Direction.NONE;
            // Update sprite direction to match movement would be handled by sprite system
        }
    }

    /**
     * Handles collision with other entities.
     *
     * @param other The entity this player collided with
     */
    @Override
    protected void handleCollision(Entity other) {
        // Switch on other.getEntityType():
        switch (other.getEntityType()) {
            case GHOST:
                // GHOST: handleGhostCollision()
                handleGhostCollision(other);
                break;
            case PELLET:
                // PELLET: collectPellet()
                collectPellet();
                other.deactivate();
                break;
            case POWER_PELLET:
                // POWER_PELLET: activatePowerUp()
                activatePowerUp();
                other.deactivate();
                break;
            case COLLECTIBLE:
                // COLLECTIBLE: collectItem() - TODO: implement if needed
                // collectItem(other);
                break;
            case OBSTACLE:
                // OBSTACLE: handleObstacleCollision() - TODO: implement if needed
                // handleObstacleCollision(other);
                break;
            default:
                // Handle unknown entity types gracefully
                break;
        }
    }

    /**
     * Handles collision with a ghost entity.
     *
     * @param ghost The ghost entity collided with
     */
    private void handleGhostCollision(Entity ghost) {
        // If isPoweredUp:
        if (isPoweredUp) {
            // "Eat" the ghost (deactivate or transform)
            if (ghost instanceof Ghost) {
                ((Ghost) ghost).getEaten();
            } else {
                ghost.deactivate(); // Fallback for non-Ghost entities
            }
            // Add bonus score
            if (scoreManager != null) {
                scoreManager.addScore("ghost");
            }
            // Trigger eating animation/sound
            isEating = true;
        } else {
            // If not powered up:
            // Lose a life with loseLife()
            loseLife();
            // Trigger death sequence would be handled by game logic
        }
    }

    /**
     * Collects a pellet and updates score.
     */
    private void collectPellet() {
        // Add pellet score using scoreManager.addScore()
        if (scoreManager != null) {
            scoreManager.addScore("pellet");
        }
        // Set isEating = true for animation
        isEating = true;
        // Trigger pellet collection sound/effect would be handled by audio system
        // Check if all pellets collected (level complete) would be handled by game logic
    }

    /**
     * Activates power-up state from power pellet.
     */
    private void activatePowerUp() {
        // Set isPoweredUp = true
        isPoweredUp = true;
        // Set powerUpTimeRemaining = POWER_UP_DURATION
        powerUpTimeRemaining = POWER_UP_DURATION;
        // Change sprite appearance (different color/animation) - could be enhanced
        // Notify skill manager of power-up activation
        if (skillManager != null) {
            skillManager.activatePowerPellet();
        }
        // Add power pellet score
        if (scoreManager != null) {
            scoreManager.addScore("power_pellet");
        }
        // Trigger power-up sound/effect would be handled by audio system
    }

    /**
     * Uses a skill if available and enabled.
     *
     * @param skillId The ID of the skill to activate
     * @return true if skill was successfully activated
     */
    public boolean activateSkill(String skillId) {
        // Check if canUseSkills and skillManager allows skill use
        if (!canUseSkills || skillManager == null) {
            return false;
        }

        // Call skillManager.activateSkill() with skillId and player reference
        boolean success = skillManager.activateSkill(skillId, this);

        // Handle skill cooldowns and resource costs would be handled by SkillManager
        // Update player state based on skill effects would be handled by skill callbacks
        // Return success status
        return success;
    }

    /**
     * Loses one life and handles death logic.
     */
    public void loseLife() {
        // Decrease lives count
        lives--;

        // If lives <= 0:
        if (lives <= 0) {
            // Set isAlive = false
            isAlive = false;
            // Trigger game over sequence would be handled by game logic
        } else {
            // If lives > 0:
            // Reset position to spawn point would be handled by game logic
            // Clear power-up state
            isPoweredUp = false;
            powerUpTimeRemaining = 0.0;
            // Trigger respawn animation would be handled by game logic
        }
        // Update UI to show remaining lives would be handled by UI system
    }

    /**
     * Restores the player to full health and lives.
     * Used for new game or cheat codes.
     */
    public void respawn() {
        // Set isAlive = true
        isAlive = true;
        // Reset lives to STARTING_LIVES
        lives = STARTING_LIVES;
        // Clear power-up state
        isPoweredUp = false;
        powerUpTimeRemaining = 0.0;
        // Reset position to spawn point would be handled by game logic
        // Clear pending movements and input state
        pendingDirection = Direction.NONE;
        for (int i = 0; i < keysPressed.length; i++) {
            keysPressed[i] = false;
        }
        // Reset sprite to normal state
        isEating = false;
        activate(); // Ensure entity is active
    }

    /**
     * Updates the power-up timer and handles expiration.
     *
     * @param deltaTime Time elapsed since last update
     */
    private void updatePowerUp(double deltaTime) {
        // If not isPoweredUp, return early
        if (!isPoweredUp) {
            return;
        }

        // Decrease powerUpTimeRemaining by deltaTime
        powerUpTimeRemaining -= deltaTime;

        // If powerUpTimeRemaining <= 0:
        if (powerUpTimeRemaining <= 0.0) {
            // Set isPoweredUp = false
            isPoweredUp = false;
            powerUpTimeRemaining = 0.0;
            // Reset sprite to normal appearance would be handled by sprite system
            // Notify skill manager of power-up expiration would be handled by SkillManager
        }
        // Handle power-up warning effects near expiration would be handled by visual effects
    }

    /**
     * Gets the player's current movement direction.
     *
     * @return The current movement direction
     */
    public Direction getCurrentDirection() {
        // If movement component exists, return movement.getCurrentDirection()
        if (movement != null) {
            return movement.getCurrentDirection();
        }
        // Otherwise return Direction.NONE
        return Direction.NONE;
    }

    /**
     * Checks if the player can currently use skills.
     *
     * @return true if skills are available
     */
    public boolean canUseSkills() {
        return canUseSkills && isAlive;
    }

    /**
     * Enables or disables skill usage.
     *
     * @param enabled true to enable skills, false to disable
     */
    public void setSkillsEnabled(boolean enabled) {
        this.canUseSkills = enabled;
    }

    /**
     * Gets the time remaining on current power-up.
     *
     * @return Power-up time remaining in seconds, or 0 if not powered up
     */
    public double getPowerUpTimeRemaining() {
        return isPoweredUp ? powerUpTimeRemaining : 0.0;
    }

    /**
     * Checks if player is currently powered up.
     *
     * @return true if powered up, false otherwise
     */
    public boolean isPoweredUp() {
        return isPoweredUp;
    }

    /**
     * Gets the number of lives remaining.
     *
     * @return Current life count
     */
    public int getLives() {
        return lives;
    }

    /**
     * Checks if the player is alive.
     *
     * @return true if alive, false if dead
     */
    public boolean isAlive() {
        return isAlive;
    }

    /**
     * Gets the current score from the score manager.
     *
     * @return Current player score
     */
    public int getScore() {
        return scoreManager != null ? scoreManager.getCurrentScore() : 0;
    }

    /**
     * Checks if player is currently in eating animation.
     *
     * @return true if eating animation is active
     */
    public boolean isEating() {
        return isEating;
    }

    /**
     * Resets eating animation state.
     * Called after animation completes.
     */
    public void clearEatingState() {
        this.isEating = false;
    }

    @Override
    public String toString() {
        return String.format("Player[id=%s, pos=%s, lives=%d, alive=%b, powered=%b]",
                getEntityId(), getPosition(), lives, isAlive, isPoweredUp);
    }
}