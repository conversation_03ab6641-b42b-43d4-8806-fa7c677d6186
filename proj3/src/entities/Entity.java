package entities;

import components.MovementComponent;
import components.Position;
import components.SpriteComponent;
import core.Pos;
import tileengine.TETile;
import tileengine.Tileset;

/**
 * Base class for all game entities with position and sprite management.
 * Provides common functionality for all game objects including players, ghosts, pellets, etc.
 * <p>
 * This class provides:
 * - Unique entity identification
 * - Position management with sub-tile precision
 * - Sprite rendering and animation
 * - Movement capabilities (optional)
 * - Basic collision detection interface
 * - Entity lifecycle management
 */
public abstract class Entity {
    // Static counter for generating unique entity IDs
    private static long nextEntityId = 1;

    // Unique identifier
    protected String entityId;

    // Core components
    protected Position position;
    protected SpriteComponent sprite;
    protected MovementComponent movement; // Optional - null for static entities

    // Entity properties
    protected boolean isActive;
    protected boolean isVisible;
    protected double collisionRadius;

    // Entity type for collision detection and game logic
    protected EntityType entityType;

    // World reference for collision detection
    protected TETile[][] world;

    /**
     * Enumeration of entity types for collision detection and game logic.
     */
    public enum EntityType {
        PLAYER("player"),
        GH<PERSON><PERSON>("ghost"),
        P<PERSON><PERSON><PERSON>("pellet"),
        POWER_PELLET("power_pellet"),
        WALL("wall"),
        COLLECTIBLE("collectible"),
        OBSTACLE("obstacle");

        private String name;

        EntityType(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * Generates a unique entity ID using a static counter.
     * This ensures all entities have unique identifiers without external coordination.
     *
     * @return A unique string identifier in the format "entity_[number]"
     */
    private static String generateEntityId() {
        return "entity_" + (nextEntityId++);
    }

    /**
     * Creates a new Entity with the specified position and sprite.
     * The entity ID is automatically generated to ensure uniqueness.
     *
     * @param startPosition Starting position in the world
     * @param sprite        Sprite component for rendering
     * @param entityType    Type of entity for game logic
     */
    public Entity(Position startPosition, SpriteComponent sprite, EntityType entityType) {
        // Validate parameters
        if (startPosition == null) {
            throw new IllegalArgumentException("Start position cannot be null");
        }
        if (entityType == null) {
            throw new IllegalArgumentException("Entity type cannot be null");
        }

        // - Set this.entityId = generateEntityId()
        this.entityId = generateEntityId();
        // - Set this.position = startPosition (or create copy)
        this.position = startPosition;
        // - Set this.sprite = sprite
        this.sprite = sprite;
        // - Set this.entityType = entityType
        this.entityType = entityType;
        // - Set isActive = true, isVisible = true
        this.isActive = true;
        this.isVisible = true;
        // - Set default collisionRadius (e.g., 0.4 for most entities)
        // TODO: we may need to extract this 0.4 constant to GameConfig
        this.collisionRadius = 0.4;
        // - Set movement = null initially (static by default)
        this.movement = null;
    }

    /**
     * Updates the entity for the current frame.
     * Called by EntityManager during update cycle.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    public void update(double deltaTime) {
        // - If not isActive, return early
        if (!isActive) {
            return;
        }
        // - Update sprite animation if sprite exists
        if (sprite != null) {
            sprite.updateAnimation(deltaTime);
        }
        // - Update movement if movement component exists
        if (movement != null) {
            movement.updatePosition(deltaTime);
        }
        // - Call updateEntity() for subclass-specific logic
        updateEntity(deltaTime);
        // TODO: Handle any state changes or cleanup
    }

    /**
     * Abstract method for subclass-specific update logic.
     * Called during the main update() method.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    protected abstract void updateEntity(double deltaTime);

    /**
     * Gets the current sprite tile for rendering.
     *
     * @return The TETile to render for this entity
     */
    public TETile getCurrentSprite() {
        // - If not isVisible, return null or transparent tile
        if (!isVisible) {
            return null;
        }
        // - If sprite component exists, return sprite.getCurrentFrame()
        if (sprite != null) {
            return sprite.getCurrentFrame();
        }
        // - Otherwise return default/fallback sprite
        return Tileset.NOTHING;
    }

    /**
     * Sets the world reference for collision detection.
     *
     * @param world The 2D world array
     */
    public void setWorld(TETile[][] world) {
        // - Validate world is not null
        if (world == null) {
            throw new IllegalArgumentException("World cannot be null");
        }
        // - Set this.world = world
        this.world = world;
        // - If movement component exists, set world reference on it too
        if (movement != null) {
            movement.setWorld(world);
        }
    }

    /**
     * Enables movement for this entity by adding a MovementComponent.
     *
     * @param maxSpeed Maximum movement speed in tiles per second
     */
    public void enableMovement(double maxSpeed) {
        // Validate maxSpeed
        if (maxSpeed <= 0) {
            throw new IllegalArgumentException("Max speed must be positive");
        }

        // - Create new MovementComponent with position and maxSpeed
        this.movement = new MovementComponent(position, maxSpeed);
        // - Set world reference on movement component if world exists
        if (world != null) {
            movement.setWorld(world);
        }
    }

    /**
     * Disables movement for this entity.
     * Used to make entities static or temporarily immobilize them.
     */
    public void disableMovement() {
        // - If movement component exists, stop movement
        if (movement != null) {
            movement.stop();
        }
        // - Set movement = null
        this.movement = null;
    }

    /**
     * Checks if this entity is colliding with another entity.
     *
     * @param other The other entity to check collision with
     * @return true if entities are colliding
     */
    public boolean isCollidingWith(Entity other) {
        if (other == null) {
            throw new IllegalArgumentException("Other entity cannot be null");
        }
        if (position == null || other.position == null) {
            throw new IllegalStateException("Entity position cannot be null");
        }
        // - Calculate distance between entity positions
        // - Compare with sum of collision radii
        // - Return true if distance <= combined radii
        if (position.distanceTo(other.position) <= collisionRadius + other.collisionRadius) {
            return true;
        }
        return false;
    }

    /**
     * Handles collision with another entity.
     * Called by EntityManager when collision is detected.
     *
     * @param other The entity this entity collided with
     */
    public void onCollision(Entity other) {
        // TODO: Update entity state if needed
        // TODO: Trigger sound effects or visual effects
        handleCollision(other);
    }

    /**
     * Abstract method for subclass-specific collision handling.
     *
     * @param other The entity this entity collided with
     */
    protected abstract void handleCollision(Entity other);

    /**
     * Activates the entity.
     * Active entities are updated and can interact with other entities.
     */
    public void activate() {
        // - Set isActive = true
        this.isActive = true;
        // - Resume movement if movement component exists
        if (movement != null) {
            movement.resume();
        }
        // - Call onActivate() for subclass-specific logic
        onActivate();
    }

    /**
     * Deactivates the entity.
     * Inactive entities are not updated and don't interact with other entities.
     */
    public void deactivate() {
        // - Set isActive = false
        this.isActive = false;
        // - Pause movement if movement component exists
        if (movement != null) {
            movement.pause();
        }
        // - Call onDeactivate() for subclass-specific logic
        onDeactivate();
    }

    /**
     * Called when entity is activated.
     * Override in subclasses for specific activation logic.
     */
    protected void onActivate() {
        // Default implementation does nothing
        // Subclasses can override for specific behavior
    }

    /**
     * Called when entity is deactivated.
     * Override in subclasses for specific deactivation logic.
     */
    protected void onDeactivate() {
        // Default implementation does nothing
        // Subclasses can override for specific behavior
    }

    /**
     * Makes the entity visible for rendering.
     */
    public void show() {
        isVisible = true;
    }

    /**
     * Makes the entity invisible (not rendered).
     */
    public void hide() {
        isVisible = false;
    }

    /**
     * Destroys the entity and cleans up resources.
     * Called when entity is removed from the game.
     */
    public void destroy() {
        this.isActive = false;
        this.isVisible = false;
        if (movement != null) {
            movement.stop();
        }
        onDestroy();
        // TODO: Clear component references
        this.sprite = null;
        this.movement = null;
        this.world = null;
        this.position = null;
    }

    /**
     * Called when entity is destroyed.
     * Override in subclasses for cleanup logic.
     */
    protected void onDestroy() {
        // Default implementation does nothing
        // Subclasses can override for cleanup
    }

    /**
     * Gets the entity's current tile position.
     *
     * @return The tile coordinates as integers
     */
    public core.Pos getTilePosition() {
        return new Pos(position.tileX(), position.tileY());
    }

    /**
     * Teleports the entity to a new position.
     *
     * @param newPosition The position to teleport to
     */
    public void teleport(Position newPosition) {
        // Validate newPosition
        if (newPosition == null) {
            throw new IllegalArgumentException("New position cannot be null");
        }

        // - Update position immediately (no movement animation)
        this.position = newPosition;
        // - Stop current movement if any
        if (movement != null) {
            movement.stop();
        }
        // - Call onTeleport() for subclass-specific logic
        onTeleport();
    }

    /**
     * Called when entity is teleported.
     * Override in subclasses for teleport-specific logic.
     */
    protected void onTeleport() {
        // Default implementation does nothing
    }

    // Getters
    public String getEntityId() {
        return entityId;
    }

    public Position getPosition() {
        return position;
    }

    public SpriteComponent getSpriteComponent() {
        return sprite;
    }

    public MovementComponent getMovementComponent() {
        return movement;
    }

    public EntityType getEntityType() {
        return entityType;
    }

    public boolean isActive() {
        return isActive;
    }

    public boolean isVisible() {
        return isVisible;
    }

    public double getCollisionRadius() {
        return collisionRadius;
    }

    public boolean canMove() {
        return movement != null;
    }

    // Setters
    public void setCollisionRadius(double radius) {
        this.collisionRadius = Math.max(0.0, radius);
    }

    @Override
    public String toString() {
        return String.format("%s[id=%s, pos=%s, type=%s, active=%b]",
                this.entityType.getName(), entityId, position, entityType, isActive);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Entity entity = (Entity) obj;
        return entityId.equals(entity.entityId);
    }

    @Override
    public int hashCode() {
        return entityId.hashCode();
    }
}