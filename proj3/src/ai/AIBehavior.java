package ai;

import entities.Entity;
import entities.Ghost;
import components.Position;
import components.MovementComponent.Direction;
import tileengine.TETile;

/**
 * Interface for ghost AI behaviors.
 * Defines common methods for all ghost AI implementations.
 * 
 * This interface provides:
 * - Standardized AI behavior contract
 * - Target management and tracking
 * - Movement decision making
 * - State-based behavior switching
 * - Player detection and line-of-sight
 */
public interface AIBehavior {
    
    /**
     * Updates the AI behavior for the current frame.
     * This is the main method called by ghosts to determine their actions.
     * 
     * @param deltaTime Time elapsed since last update in seconds
     * @param ghost The ghost entity using this AI behavior
     * @param world The current world state for pathfinding
     */
    void update(double deltaTime, Ghost ghost, TETile[][] world);
    
    /**
     * Gets the next movement direction for the ghost.
     * Called after update() to determine where the ghost should move.
     * 
     * @param ghost The ghost requesting movement direction
     * @param world The current world state
     * @return Direction the ghost should move, or NONE if no movement
     */
    Direction getNextMove(Ghost ghost, TETile[][] world);
    
    /**
     * Sets the target entity for this AI behavior.
     * Usually set to the player entity for tracking behaviors.
     * 
     * @param target The entity to target (typically the player)
     */
    void setTarget(Entity target);
    
    /**
     * Gets the current target entity.
     * 
     * @return The current target entity, or null if no target
     */
    Entity getTarget();
    
    /**
     * Checks if the ghost can see the player from its current position.
     * Uses line-of-sight calculation with wall collision detection.
     * 
     * @param ghost The ghost checking for line of sight
     * @param target The target entity to check visibility to
     * @param world The world for wall collision checking
     * @return true if target is visible, false if blocked by walls
     */
    boolean canSeePlayer(Ghost ghost, Entity target, TETile[][] world);
    
    /**
     * Called when the ghost enters frightened state.
     * AI should switch to fleeing behavior.
     * 
     * @param ghost The ghost entering frightened state
     * @param duration Duration of frightened state in seconds
     */
    void onFrightened(Ghost ghost, double duration);
    
    /**
     * Called when the ghost exits frightened state.
     * AI should return to normal behavior patterns.
     * 
     * @param ghost The ghost returning to normal state
     */
    void onReturnToNormal(Ghost ghost);
    
    /**
     * Called when the ghost is eaten and needs to return to base.
     * AI should pathfind directly back to the ghost's home base.
     * 
     * @param ghost The ghost that was eaten
     */
    void onEaten(Ghost ghost);
    
    /**
     * Called when the ghost respawns at its home base.
     * AI should reset to initial state and behavior.
     * 
     * @param ghost The ghost that respawned
     */
    void onRespawn(Ghost ghost);
    
    /**
     * Gets a description of this AI behavior.
     * Used for debugging and UI display.
     * 
     * @return String description of the AI behavior
     */
    String getDescription();
    
    /**
     * Gets the priority level of this AI behavior.
     * Higher priority behaviors may override lower priority ones.
     * 
     * @return Priority level (0 = lowest, 10 = highest)
     */
    int getPriority();
    
    /**
     * Checks if this AI behavior is currently active.
     * 
     * @return true if AI is actively controlling ghost movement
     */
    boolean isActive();
    
    /**
     * Activates or deactivates this AI behavior.
     * 
     * @param active true to activate, false to deactivate
     */
    void setActive(boolean active);
    
    /**
     * Resets the AI behavior to its initial state.
     * Clears any cached pathfinding, targets, or temporary state.
     */
    void reset();
    
    /**
     * Gets the current AI state for debugging.
     * 
     * @return Map containing AI state information
     */
    java.util.Map<String, Object> getDebugInfo();
}