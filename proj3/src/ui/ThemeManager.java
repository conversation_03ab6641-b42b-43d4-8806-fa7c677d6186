package ui;

import java.awt.Color;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Theme management system for the Pac-Man game.
 * Provides four essential themes: Default, Monokai, Tokyo Night, and Gruvbox.
 * <p>
 * This manager provides:
 * - Four predefined color themes
 * - Real-time theme switching
 * - Theme listener notifications
 * - Color palette management for UI elements
 */
public class ThemeManager {

    /**
     * Available theme types.
     */
    public enum ThemeType {
        DEFAULT("Default", "Standard Java AWT colors"),
        MONOKAI("Monokai", "Classic dark theme with vibrant colors"),
        TOKYO_NIGHT("Tokyo Night", "Modern dark theme with blue accents"),
        GRUVBOX("Gruvbox", "Retro dark theme with warm colors");

        private final String displayName;
        private final String description;

        ThemeType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * Interface for theme change listeners.
     */
    public interface ThemeListener {
        void onThemeChanged(ThemeType newTheme, Map<String, Color> colors);
    }

    /**
     * Theme data container.
     */
    public static class Theme {
        public final ThemeType type;
        public final String name;
        public final String description;
        public final Map<String, Color> colors;

        public Theme(ThemeType type, Map<String, Color> colors) {
            this.type = type;
            this.name = type.getDisplayName();
            this.description = type.getDescription();
            this.colors = new HashMap<>(colors);
        }

        public Color getColor(String key) {
            return colors.getOrDefault(key, Color.WHITE);
        }
    }

    // Current theme state
    private ThemeType currentTheme;
    private Map<String, Color> currentColors;

    // Theme definitions
    private Map<ThemeType, Map<String, Color>> themes;

    // Theme listeners
    private List<ThemeListener> listeners;

    /**
     * Creates a ThemeManager with Default theme.
     */
    public ThemeManager() {
        this.currentTheme = ThemeType.DEFAULT;
        this.themes = new HashMap<>();
        this.listeners = new ArrayList<>();

        initializeThemes();
        applyTheme(ThemeType.DEFAULT);
    }

    /**
     * Initializes all predefined themes.
     */
    private void initializeThemes() {
        initializeDefaultTheme();
        initializeMonokaiTheme();
        initializeTokyoNightTheme();
        initializeGruvboxTheme();
    }

    /**
     * Initializes the Default theme using standard Java AWT colors.
     */
    private void initializeDefaultTheme() {
        Map<String, Color> colors = new HashMap<>();

        // Basic colors
        colors.put("background", Color.WHITE);
        colors.put("foreground", Color.BLACK);
        colors.put("primary", Color.BLUE);
        colors.put("secondary", Color.GRAY);
        colors.put("accent", Color.RED);

        // UI element colors
        colors.put("text", Color.BLACK);
        colors.put("text_secondary", Color.DARK_GRAY);
        colors.put("border", Color.GRAY);
        colors.put("button_bg", Color.LIGHT_GRAY);
        colors.put("button_fg", Color.BLACK);
        colors.put("menu_bg", Color.WHITE);
        colors.put("menu_fg", Color.BLACK);

        // Game element colors
        colors.put("wall", Color.BLUE);
        colors.put("floor", Color.LIGHT_GRAY);
        colors.put("player", Color.YELLOW);
        colors.put("ghost", Color.RED);
        colors.put("pellet", Color.WHITE);
        colors.put("power_pellet", Color.YELLOW);

        themes.put(ThemeType.DEFAULT, colors);
    }

    /**
     * Initializes the Monokai theme.
     */
    private void initializeMonokaiTheme() {
        Map<String, Color> colors = new HashMap<>();

        // Base colors: #272822 (bg), #F8F8F2 (fg), #F92672 (pink), #A6E22E (green), #FD971F (orange)
        colors.put("background", new Color(0x272822));
        colors.put("foreground", new Color(0xF8F8F2));
        colors.put("primary", new Color(0xF92672));
        colors.put("secondary", new Color(0x75715E));
        colors.put("accent", new Color(0xFD971F));

        // UI element colors
        colors.put("text", new Color(0xF8F8F2));
        colors.put("text_secondary", new Color(0x75715E));
        colors.put("border", new Color(0x49483E));
        colors.put("button_bg", new Color(0x49483E));
        colors.put("button_fg", new Color(0xF8F8F2));
        colors.put("menu_bg", new Color(0x272822));
        colors.put("menu_fg", new Color(0xF8F8F2));

        // Game element colors
        colors.put("wall", new Color(0x66D9EF));
        colors.put("floor", new Color(0x272822));
        colors.put("player", new Color(0xFD971F));
        colors.put("ghost", new Color(0xF92672));
        colors.put("pellet", new Color(0xF8F8F2));
        colors.put("power_pellet", new Color(0xA6E22E));

        themes.put(ThemeType.MONOKAI, colors);
    }

    /**
     * Initializes the Tokyo Night theme.
     */
    private void initializeTokyoNightTheme() {
        Map<String, Color> colors = new HashMap<>();

        // Base colors: #1a1b26 (bg), #c0caf5 (fg), #7aa2f7 (blue), #9ece6a (green), #f7768e (red)
        colors.put("background", new Color(0x1a1b26));
        colors.put("foreground", new Color(0xc0caf5));
        colors.put("primary", new Color(0x7aa2f7));
        colors.put("secondary", new Color(0x565f89));
        colors.put("accent", new Color(0xf7768e));

        // UI element colors
        colors.put("text", new Color(0xc0caf5));
        colors.put("text_secondary", new Color(0x565f89));
        colors.put("border", new Color(0x414868));
        colors.put("button_bg", new Color(0x414868));
        colors.put("button_fg", new Color(0xc0caf5));
        colors.put("menu_bg", new Color(0x1a1b26));
        colors.put("menu_fg", new Color(0xc0caf5));

        // Game element colors
        colors.put("wall", new Color(0x7aa2f7));
        colors.put("floor", new Color(0x1a1b26));
        colors.put("player", new Color(0xe0af68));
        colors.put("ghost", new Color(0xf7768e));
        colors.put("pellet", new Color(0xc0caf5));
        colors.put("power_pellet", new Color(0x9ece6a));

        themes.put(ThemeType.TOKYO_NIGHT, colors);
    }

    /**
     * Initializes the Gruvbox theme.
     */
    private void initializeGruvboxTheme() {
        Map<String, Color> colors = new HashMap<>();

        // Base colors: #282828 (bg), #ebdbb2 (fg), #458588 (blue), #98971a (green), #cc241d (red)
        colors.put("background", new Color(0x282828));
        colors.put("foreground", new Color(0xebdbb2));
        colors.put("primary", new Color(0x458588));
        colors.put("secondary", new Color(0x928374));
        colors.put("accent", new Color(0xcc241d));

        // UI element colors
        colors.put("text", new Color(0xebdbb2));
        colors.put("text_secondary", new Color(0x928374));
        colors.put("border", new Color(0x504945));
        colors.put("button_bg", new Color(0x504945));
        colors.put("button_fg", new Color(0xebdbb2));
        colors.put("menu_bg", new Color(0x282828));
        colors.put("menu_fg", new Color(0xebdbb2));

        // Game element colors
        colors.put("wall", new Color(0x458588));
        colors.put("floor", new Color(0x282828));
        colors.put("player", new Color(0xd79921));
        colors.put("ghost", new Color(0xcc241d));
        colors.put("pellet", new Color(0xebdbb2));
        colors.put("power_pellet", new Color(0x98971a));

        themes.put(ThemeType.GRUVBOX, colors);
    }

    /**
     * Applies a theme and notifies listeners.
     */
    public void applyTheme(ThemeType themeType) {
        if (!themes.containsKey(themeType)) {
            System.err.println("Warning: Theme " + themeType + " not found, using DEFAULT");
            themeType = ThemeType.DEFAULT;
        }

        this.currentTheme = themeType;
        this.currentColors = themes.get(themeType);

        notifyThemeListeners();
    }

    /**
     * Gets the current theme.
     */
    public ThemeType getCurrentTheme() {
        return currentTheme;
    }

    /**
     * Gets the current theme as a Theme object.
     */
    public Theme getCurrentThemeData() {
        return new Theme(currentTheme, currentColors);
    }

    /**
     * Gets a color from the current theme.
     */
    public Color getColor(String key) {
        return currentColors.getOrDefault(key, Color.WHITE);
    }

    /**
     * Gets all colors from the current theme.
     */
    public Map<String, Color> getAllColors() {
        return new HashMap<>(currentColors);
    }

    /**
     * Gets available themes.
     */
    public ThemeType[] getAvailableThemes() {
        return ThemeType.values();
    }

    /**
     * Checks if a theme is available.
     */
    public boolean isThemeAvailable(ThemeType themeType) {
        return themes.containsKey(themeType);
    }

    /**
     * Adds a theme listener.
     */
    public void addThemeListener(ThemeListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    /**
     * Removes a theme listener.
     */
    public void removeThemeListener(ThemeListener listener) {
        listeners.remove(listener);
    }

    /**
     * Notifies all theme listeners of theme changes.
     */
    private void notifyThemeListeners() {
        for (ThemeListener listener : listeners) {
            try {
                listener.onThemeChanged(currentTheme, currentColors);
            } catch (Exception e) {
                System.err.println("Error notifying theme listener: " + e.getMessage());
            }
        }
    }

    /**
     * Gets theme information as a string.
     */
    public String getThemeInfo() {
        return String.format("Current theme: %s (%s) - %d colors loaded",
                currentTheme.getDisplayName(),
                currentTheme.getDescription(),
                currentColors.size());
    }
}