package rendering;

import java.util.HashMap;
import java.util.Map;

import components.Position;
import entities.Entity;

/**
 * Camera management system for infinite scrolling and viewport control.
 * Handles camera positioning, entity following, and viewport calculations.
 * <p>
 * This system provides:
 * - Entity following with smooth interpolation
 * - Manual camera positioning and offset control
 * - Viewport boundary management
 * - Coordinate transformations between world and screen space
 * - Camera bounds and constraints
 */
public class CameraSystem {
    // Camera position and state
    private Position cameraPosition;      // Center of camera view
    private Position targetPosition;      // Where camera is moving towards
    private Entity followTarget;          // Entity to follow (usually player)

    // Camera movement settings
    private double followSpeed;           // Speed of camera following
    private boolean smoothFollowing;      // Enable smooth camera movement
    private double followDeadzone;        // Deadzone around target before camera moves

    // Viewport settings
    private int viewportWidth;            // Viewport width in tiles
    private int viewportHeight;           // Viewport height in tiles
    private double pixelsPerTile;         // Pixels per tile for scaling

    // Camera bounds and constraints
    private boolean hasBounds;
    private double minX, maxX, minY, maxY; // World bounds for camera

    // Camera effects and state
    private boolean isShaking;
    private double shakeIntensity;
    private double shakeDuration;
    private double shakeTimeRemaining;
    private Position shakeOffset;

    // Zoom settings (for future expansion)
    private double zoomLevel;
    private double targetZoomLevel;
    private boolean smoothZooming;

    /**
     * Creates a CameraSystem with default settings.
     *
     * @param viewportWidth  Width of viewport in tiles
     * @param viewportHeight Height of viewport in tiles
     */
    public CameraSystem(int viewportWidth, int viewportHeight) {
        // Set this.viewportWidth = viewportWidth, viewportHeight = viewportHeight
        this.viewportWidth = viewportWidth;
        this.viewportHeight = viewportHeight;
        // Set pixelsPerTile = 16 (from TERenderer TILE_SIZE)
        this.pixelsPerTile = 16.0; // TERenderer.TILE_SIZE
        // Initialize cameraPosition = new Position(0, 0)
        this.cameraPosition = new Position(0, 0);
        // Initialize targetPosition = new Position(0, 0)
        this.targetPosition = new Position(0, 0);
        // Set followSpeed = 5.0 (reasonable default)
        this.followSpeed = 5.0;
        // Set smoothFollowing = true, followDeadzone = 2.0
        this.smoothFollowing = true;
        this.followDeadzone = 2.0;
        // Set hasBounds = false
        this.hasBounds = false;
        // Initialize shake and zoom variables
        this.isShaking = false;
        this.shakeIntensity = 0.0;
        this.shakeDuration = 0.0;
        this.shakeTimeRemaining = 0.0;
        this.shakeOffset = new Position(0, 0);
        this.zoomLevel = 1.0;
        this.targetZoomLevel = 1.0;
        this.smoothZooming = false;
    }

    /**
     * Sets the entity for the camera to follow.
     *
     * @param entity The entity to follow (typically the player)
     */
    public void followEntity(Entity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("Entity cannot be null");
        }
        // - Set this.followTarget = entity
        this.followTarget = entity;
        // - If entity is not null:
        //   - Set targetPosition to entity's position
        //   - If not smoothFollowing, immediately move camera to entity
        this.targetPosition = entity.getPosition();
        if (!smoothFollowing) {
            this.cameraPosition = entity.getPosition();
        }
    }

    /**
     * Updates the camera position and effects.
     * Should be called every frame.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    public void update(double deltaTime) {
        // Update camera following with updateFollowing()
        updateFollowing(deltaTime);
        // Update camera shake with updateShake()
        updateShake(deltaTime);
        // Update zoom with updateZoom()
        updateZoom(deltaTime);
        // Apply camera bounds constraints
        if (hasBounds) {
            applyBounds();
        }
        // Update final camera position
        this.cameraPosition = this.targetPosition;
    }

    /**
     * Updates camera following behavior.
     *
     * @param deltaTime Time elapsed since last update
     */
    private void updateFollowing(double deltaTime) {
        // If followTarget is null, return early
        if (followTarget == null) {
            return;
        }

        // Get target entity position
        Position entityPos = followTarget.getPosition();
        if (entityPos == null) {
            return;
        }

        // Calculate distance to target
        double distance = cameraPosition.distanceTo(entityPos);

        // If distance > followDeadzone:
        if (distance > followDeadzone) {
            // Update targetPosition to entity position
            this.targetPosition = entityPos;

            // If smoothFollowing:
            if (smoothFollowing && followSpeed > 0) {
                // Interpolate camera toward target with followSpeed
                double deltaX = entityPos.getX() - cameraPosition.getX();
                double deltaY = entityPos.getY() - cameraPosition.getY();

                double moveAmount = followSpeed * deltaTime;
                double factor = Math.min(moveAmount / distance, 1.0);

                double newX = cameraPosition.getX() + deltaX * factor;
                double newY = cameraPosition.getY() + deltaY * factor;
                this.targetPosition = new Position(newX, newY);
            }
            // Else: Immediately move camera to target (targetPosition already set)
        }
    }

    /**
     * Updates camera shake effects.
     *
     * @param deltaTime Time elapsed since last update
     */
    private void updateShake(double deltaTime) {
        // If not isShaking, return early
        if (!isShaking) {
            return;
        }

        // Decrease shakeTimeRemaining by deltaTime
        this.shakeTimeRemaining -= deltaTime;

        // If shake time expired:
        if (this.shakeTimeRemaining <= 0) {
            // Stop shake with stopShake()
            stopShake();
        } else {
            // Calculate shake offset based on intensity and time
            // Update shakeOffset with random values
            double maxOffset = shakeIntensity * 2.0; // Max shake distance
            double offsetX = (Math.random() - 0.5) * 2.0 * maxOffset;
            double offsetY = (Math.random() - 0.5) * 2.0 * maxOffset;
            this.shakeOffset = new Position(offsetX, offsetY);
        }
    }

    /**
     * Updates camera zoom effects.
     *
     * @param deltaTime Time elapsed since last update
     */
    private void updateZoom(double deltaTime) {
        // If not smoothZooming or zoomLevel == targetZoomLevel, return early
        if (!smoothZooming || Math.abs(zoomLevel - targetZoomLevel) < 0.001) {
            return;
        }

        // Interpolate zoomLevel toward targetZoomLevel
        double zoomSpeed = 2.0; // Zoom interpolation speed
        double deltaZoom = targetZoomLevel - zoomLevel;
        double zoomChange = deltaZoom * zoomSpeed * deltaTime;

        if (Math.abs(zoomChange) >= Math.abs(deltaZoom)) {
            // Close enough, snap to target
            this.zoomLevel = targetZoomLevel;
            this.smoothZooming = false;
        } else {
            this.zoomLevel += zoomChange;
        }

        // Update pixelsPerTile based on zoom level
        this.pixelsPerTile = 16.0 * zoomLevel;

        // Clamp zoom to reasonable limits
        this.zoomLevel = Math.max(0.5, Math.min(3.0, this.zoomLevel));
    }

    /**
     * Manually sets the camera position.
     *
     * @param x World X coordinate for camera center
     * @param y World Y coordinate for camera center
     */
    public void setPosition(double x, double y) {
        // Set cameraPosition to new Position(x, y)
        this.cameraPosition = new Position(x, y);
        // Set targetPosition to same position
        this.targetPosition = new Position(x, y);
        // Apply bounds constraints if hasBounds is true
        if (hasBounds) {
            applyBounds();
        }
    }

    /**
     * Sets the camera offset relative to current position.
     *
     * @param offsetX X offset to apply
     * @param offsetY Y offset to apply
     */
    public void setOffset(double offsetX, double offsetY) {
        // Calculate new position: current + offset
        double newX = cameraPosition.getX() + offsetX;
        double newY = cameraPosition.getY() + offsetY;
        // Call setPosition() with new coordinates
        setPosition(newX, newY);
    }

    /**
     * Converts world coordinates to screen coordinates.
     *
     * @param worldX World X coordinate
     * @param worldY World Y coordinate
     * @return Array containing [screenX, screenY]
     */
    public double[] worldToScreen(double worldX, double worldY) {
        // Calculate relative position: world - camera position
        double relativeX = worldX - cameraPosition.getX();
        double relativeY = worldY - cameraPosition.getY();

        // Apply camera shake offset if active
        if (isShaking && shakeOffset != null) {
            relativeX += shakeOffset.getX();
            relativeY += shakeOffset.getY();
        }

        // Convert to screen pixels: relative * pixelsPerTile
        double screenX = relativeX * pixelsPerTile;
        double screenY = relativeY * pixelsPerTile;

        // Add screen center offset: + (viewportWidth/2, viewportHeight/2)
        screenX += viewportWidth / 2.0;
        screenY += viewportHeight / 2.0;

        // Return screen coordinates
        return new double[]{screenX, screenY};
    }

    /**
     * Converts screen coordinates to world coordinates.
     *
     * @param screenX Screen X coordinate
     * @param screenY Screen Y coordinate
     * @return Array containing [worldX, worldY]
     */
    public double[] screenToWorld(double screenX, double screenY) {
        // Subtract screen center offset: screen - (viewportWidth/2, viewportHeight/2)
        double relativeX = screenX - (viewportWidth / 2.0);
        double relativeY = screenY - (viewportHeight / 2.0);

        // Convert to world units: screen / pixelsPerTile
        double worldX = relativeX / pixelsPerTile;
        double worldY = relativeY / pixelsPerTile;

        // Add camera position: relative + camera position
        worldX += cameraPosition.getX();
        worldY += cameraPosition.getY();

        // Subtract camera shake offset if active
        if (isShaking && shakeOffset != null) {
            worldX -= shakeOffset.getX();
            worldY -= shakeOffset.getY();
        }

        // Return world coordinates
        return new double[]{worldX, worldY};
    }

    /**
     * Starts camera shake effect.
     *
     * @param intensity Shake intensity (0.0 to 1.0)
     * @param duration  Duration of shake in seconds
     */
    public void startShake(double intensity, double duration) {
        // Set isShaking = true
        this.isShaking = true;
        // Set shakeIntensity = intensity (clamped to [0.0, 1.0])
        this.shakeIntensity = Math.max(0.0, Math.min(1.0, intensity));
        // Set shakeDuration = duration, shakeTimeRemaining = duration
        this.shakeDuration = duration;
        this.shakeTimeRemaining = duration;
        // Initialize shakeOffset = new Position(0, 0)
        this.shakeOffset = new Position(0, 0);
    }

    /**
     * Stops camera shake effect immediately.
     */
    public void stopShake() {
        // Set isShaking = false
        this.isShaking = false;
        // Set shakeIntensity = 0, shakeTimeRemaining = 0
        this.shakeIntensity = 0.0;
        this.shakeTimeRemaining = 0.0;
        // Reset shakeOffset to (0, 0)
        this.shakeOffset = new Position(0, 0);
    }

    /**
     * Sets camera bounds to constrain movement.
     *
     * @param minX Minimum X world coordinate
     * @param maxX Maximum X world coordinate
     * @param minY Minimum Y world coordinate
     * @param maxY Maximum Y world coordinate
     */
    public void setBounds(double minX, double maxX, double minY, double maxY) {
        // Validate bounds (min < max)
        if (minX >= maxX || minY >= maxY) {
            throw new IllegalArgumentException("Invalid bounds: min values must be less than max values");
        }

        // Set this.minX = minX, maxX = maxX, etc.
        this.minX = minX;
        this.maxX = maxX;
        this.minY = minY;
        this.maxY = maxY;
        // Set hasBounds = true
        this.hasBounds = true;

        // Apply bounds to current camera position
        applyBounds();
    }

    /**
     * Removes camera bounds constraints.
     */
    public void clearBounds() {
        // Set hasBounds = false
        this.hasBounds = false;
        // Reset bound values to extreme values
        this.minX = Double.NEGATIVE_INFINITY;
        this.maxX = Double.POSITIVE_INFINITY;
        this.minY = Double.NEGATIVE_INFINITY;
        this.maxY = Double.POSITIVE_INFINITY;
    }

    /**
     * Applies bounds constraints to camera position.
     */
    private void applyBounds() {
        // If not hasBounds, return early
        if (!hasBounds) {
            return;
        }

        // Calculate viewport half-size for proper bounds checking
        double halfWidth = viewportWidth / 2.0;
        double halfHeight = viewportHeight / 2.0;

        // Clamp camera position X to [minX + halfWidth, maxX - halfWidth]
        double clampedX = Math.max(minX + halfWidth, Math.min(maxX - halfWidth, cameraPosition.getX()));
        // Clamp camera position Y to [minY + halfHeight, maxY - halfHeight]
        double clampedY = Math.max(minY + halfHeight, Math.min(maxY - halfHeight, cameraPosition.getY()));

        // Update both cameraPosition and targetPosition
        this.cameraPosition = new Position(clampedX, clampedY);
        this.targetPosition = new Position(clampedX, clampedY);
    }

    /**
     * Sets the zoom level for the camera.
     *
     * @param zoomLevel New zoom level (1.0 = normal, 2.0 = 2x zoom, etc.)
     * @param smooth    Whether to smoothly transition to new zoom
     */
    public void setZoom(double zoomLevel, boolean smooth) {
        // Clamp zoomLevel to reasonable range (0.5 to 3.0)
        zoomLevel = Math.max(0.5, Math.min(3.0, zoomLevel));

        // If smooth:
        if (smooth) {
            // Set targetZoomLevel = zoomLevel
            this.targetZoomLevel = zoomLevel;
            // Enable smoothZooming
            this.smoothZooming = true;
        } else {
            // Set this.zoomLevel = zoomLevel immediately
            this.zoomLevel = zoomLevel;
            this.targetZoomLevel = zoomLevel;
            // Update pixelsPerTile = baseTileSize * zoomLevel
            this.pixelsPerTile = 16.0 * zoomLevel;
            this.smoothZooming = false;
        }
    }

    /**
     * Sets the follow speed for smooth camera movement.
     *
     * @param speed Follow speed (higher = faster, 0 = instant)
     */
    public void setFollowSpeed(double speed) {
        // Set this.followSpeed = Math.max(0.0, speed)
        this.followSpeed = Math.max(0.0, speed);
        // If speed == 0, disable smooth following
        if (speed == 0.0) {
            this.smoothFollowing = false;
        }
    }

    /**
     * Sets the deadzone radius for camera following.
     *
     * @param deadzone Deadzone radius in world units
     */
    public void setFollowDeadzone(double deadzone) {
        // Set this.followDeadzone = Math.max(0.0, deadzone)
        this.followDeadzone = Math.max(0.0, deadzone);
    }

    /**
     * Checks if a world position is currently visible on screen.
     *
     * @param worldX World X coordinate
     * @param worldY World Y coordinate
     * @return True if position is visible
     */
    public boolean isPositionVisible(double worldX, double worldY) {
        // Convert world position to screen coordinates using our existing method
        double[] screenPos = worldToScreen(worldX, worldY);

        // Check if within viewport bounds (assuming standard screen dimensions)
        return screenPos[0] >= 0 && screenPos[0] < 80 && screenPos[1] >= 0 && screenPos[1] < 30;
    }

    /**
     * Gets the visible world bounds for the current camera position.
     *
     * @return Array containing [minX, maxX, minY, maxY] of visible area
     */
    public double[] getVisibleBounds() {
        // Calculate viewport half-dimensions in world units
        // Standard viewport is 80x30 tiles, account for zoom
        double halfWidth = (80.0 / 2.0) / zoomLevel;
        double halfHeight = (30.0 / 2.0) / zoomLevel;

        // Calculate bounds: camera position +/- half-dimensions
        double minX = cameraPosition.getX() - halfWidth;
        double maxX = cameraPosition.getX() + halfWidth;
        double minY = cameraPosition.getY() - halfHeight;
        double maxY = cameraPosition.getY() + halfHeight;

        return new double[]{minX, maxX, minY, maxY};
    }

    // Getters
    public Position getCameraPosition() {
        return cameraPosition;
    }

    public Position getTargetPosition() {
        return targetPosition;
    }

    public Entity getFollowTarget() {
        return followTarget;
    }

    public double getFollowSpeed() {
        return followSpeed;
    }

    public double getFollowDeadzone() {
        return followDeadzone;
    }

    public int getViewportWidth() {
        return viewportWidth;
    }

    public int getViewportHeight() {
        return viewportHeight;
    }

    public double getZoomLevel() {
        return zoomLevel;
    }

    public boolean isSmoothFollowing() {
        return smoothFollowing;
    }

    public boolean isShaking() {
        return isShaking;
    }

    public boolean hasBounds() {
        return hasBounds;
    }

    // Setters
    public void setSmoothFollowing(boolean smooth) {
        this.smoothFollowing = smooth;
    }

    /**
     * Gets camera system status and statistics.
     *
     * @return Map containing camera system information
     */
    public Map<String, Object> getCameraStats() {
        // Collect camera statistics
        Map<String, Object> stats = new HashMap<>();
        stats.put("Position", new double[]{cameraPosition.getX(), cameraPosition.getY()});
        stats.put("Target", new double[]{targetPosition.getX(), targetPosition.getY()});
        stats.put("Zoom", new double[]{zoomLevel, targetZoomLevel});
        stats.put("Following", followTarget != null ? "Yes" : "No");
        if (followTarget != null) {
            stats.put("Follow Speed", followSpeed);
            stats.put("Deadzone", followDeadzone);
        }
        stats.put("Shaking", isShaking ? "Yes" : "No");
        if (isShaking) {
            stats.put("Shake Intensity", shakeIntensity);
            stats.put("Shake Time Remaining", shakeTimeRemaining);
        }
        stats.put("Bounds", hasBounds ? "Yes" : "No");
        if (hasBounds) {
            stats.put("Bounds Info", new double[]{this.minX, this.minY, this.maxX, this.maxY});
        }
        return stats;
    }
}