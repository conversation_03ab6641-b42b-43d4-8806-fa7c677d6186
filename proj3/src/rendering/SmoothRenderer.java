package rendering;

import java.awt.Color;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import components.Position;
import edu.princeton.cs.algs4.StdDraw;
import entities.Entity;
import tileengine.TERenderer;
import tileengine.TETile;

/**
 * Enhanced renderer supporting sub-tile positioning and camera systems.
 * Extends TERenderer to add smooth movement, camera support, and entity rendering.
 * <p>
 * This renderer provides:
 * - Sub-tile entity positioning and interpolation
 * - Camera offset support for scrolling
 * - Smooth entity movement rendering
 * - Enhanced tile rendering with effects
 * - Integration with existing TERenderer system
 */
public class SmoothRenderer extends TERenderer {
    // Camera system integration
    private CameraSystem cameraSystem;
    private double cameraOffsetX;
    private double cameraOffsetY;

    // Rendering settings
    private boolean smoothRenderingEnabled;
    private boolean entityInterpolationEnabled;
    private double interpolationAlpha; // For frame interpolation

    // Entity rendering
    private List<Entity> entitiesToRender;

    // Rendering statistics
    private int tilesRendered;
    private int entitiesRendered;
    private long lastRenderTime;

    // Rendering effects
    private boolean enableEffects;
    private double effectTime; // For animated effects

    /**
     * Creates a SmoothRenderer with default settings.
     */
    public SmoothRenderer() {
        super();
        // Set smoothRenderingEnabled = true
        this.smoothRenderingEnabled = true;
        // Set entityInterpolationEnabled = true
        this.entityInterpolationEnabled = true;
        // Set interpolationAlpha = 1.0 (no interpolation by default)
        this.interpolationAlpha = 1.0;
        // Set cameraOffsetX = 0, cameraOffsetY = 0
        this.cameraOffsetX = 0.0;
        this.cameraOffsetY = 0.0;
        // Set enableEffects = true
        this.enableEffects = true;
        // Initialize rendering statistics
        this.tilesRendered = 0;
        this.entitiesRendered = 0;
        this.lastRenderTime = System.currentTimeMillis();
        this.effectTime = 0.0;
    }

    /**
     * Creates a SmoothRenderer with camera system integration.
     *
     * @param cameraSystem The camera system for offset calculations
     */
    public SmoothRenderer(CameraSystem cameraSystem) {
        // Call default constructor
        this();
        // Set this.cameraSystem = cameraSystem
        this.cameraSystem = cameraSystem;
        // Initialize camera offset from camera system
        if (cameraSystem != null) {
            Position cameraPos = cameraSystem.getCameraPosition();
            if (cameraPos != null) {
                this.cameraOffsetX = cameraPos.getX();
                this.cameraOffsetY = cameraPos.getY();
            }
        }
    }

    /**
     * Sets the camera system for offset calculations.
     *
     * @param cameraSystem The camera system to use
     */
    public void setCameraSystem(CameraSystem cameraSystem) {
        // Set this.cameraSystem = cameraSystem
        this.cameraSystem = cameraSystem;
        // Update camera offsets from camera system
        if (cameraSystem != null) {
            Position cameraPos = cameraSystem.getCameraPosition();
            if (cameraPos != null) {
                this.cameraOffsetX = cameraPos.getX();
                this.cameraOffsetY = cameraPos.getY();
            }
        }
    }

    /**
     * Sets the camera offset manually.
     *
     * @param offsetX X offset in tiles
     * @param offsetY Y offset in tiles
     */
    public void setCameraOffset(double offsetX, double offsetY) {
        // Set this.cameraOffsetX = offsetX
        this.cameraOffsetX = offsetX;
        // Set this.cameraOffsetY = offsetY
        this.cameraOffsetY = offsetY;
        // Update rendering calculations to use new offset
    }

    /**
     * Renders a frame with camera offset and entity support.
     * Extends the base TERenderer functionality.
     *
     * @param world The world tile array to render
     */
    @Override
    public void renderFrame(TETile[][] world) {
        long frameStartTime = System.currentTimeMillis();

        // Update camera offset from camera system if available
        if (cameraSystem != null) {
            Position cameraPos = cameraSystem.getCameraPosition();
            if (cameraPos != null) {
                this.cameraOffsetX = cameraPos.getX();
                this.cameraOffsetY = cameraPos.getY();
            }
        }

        // Clear screen with appropriate background color
        StdDraw.clear(new Color(0, 0, 0));

        // Render world tiles with camera offset using renderWorldWithOffset()
        renderWorldWithOffset(world);

        // Render all entities with renderEntities()
        if (entitiesToRender != null) {
            renderEntities(entitiesToRender);
        }

        // Update rendering statistics
        this.lastRenderTime = System.currentTimeMillis() - frameStartTime;

        // Call StdDraw.show() to display frame
        StdDraw.show();
    }

    /**
     * Renders the world with camera offset applied.
     *
     * @param world The world tile array to render
     */
    private void renderWorldWithOffset(TETile[][] world) {
        if (world == null || world.length == 0) {
            return;
        }

        int numXTiles = world.length;
        int numYTiles = world[0].length;
        this.tilesRendered = 0;

        // Calculate visible tile range based on camera offset and screen size
        // For each visible tile:
        for (int x = 0; x < numXTiles; x++) {
            for (int y = 0; y < numYTiles; y++) {
                if (world[x][y] == null) {
                    continue;
                }

                // Calculate screen position with camera offset
                double screenX = x - cameraOffsetX;
                double screenY = y - cameraOffsetY;

                // Skip tiles outside visible area for performance
                if (screenX < -1 || screenX > 100 || screenY < -1 || screenY > 100) {
                    continue;
                }

                // Render tile at calculated position
                world[x][y].draw(screenX, screenY);
                // Update tilesRendered counter
                this.tilesRendered++;

                // Apply effects if enabled
                if (enableEffects) {
                    applyTileEffects(world[x][y], screenX, screenY);
                }
            }
        }
        // Handle world boundaries gracefully
    }

    /**
     * Renders an entity at its interpolated position.
     *
     * @param entity The entity to render
     */
    public void renderEntity(Entity entity) {
        // If not entity.isVisible(), return early
        if (entity == null || !entity.isVisible()) {
            return;
        }

        // Get entity position (current or interpolated)
        Position entityPos;
        if (entityInterpolationEnabled && interpolationAlpha < 1.0) {
            entityPos = calculateInterpolatedPosition(entity, interpolationAlpha);
        } else {
            entityPos = entity.getPosition();
        }

        if (entityPos == null) {
            return;
        }

        // Calculate screen position with camera offset
        double screenX = entityPos.getX() - cameraOffsetX;
        double screenY = entityPos.getY() - cameraOffsetY;

        // Get entity sprite with entity.getCurrentSprite()
        TETile sprite = entity.getCurrentSprite();
        if (sprite == null) {
            return;
        }

        // Render sprite at calculated position with proper scaling
        sprite.draw(screenX, screenY);
        // Handle entity effects (flashing, transparency, etc.)
    }

    /**
     * Renders all entities in the provided list.
     *
     * @param entities List of entities to render
     */
    public void renderEntities(List<Entity> entities) {
        if (entities == null || entities.isEmpty()) {
            return;
        }

        this.entitiesRendered = 0;

        // Sort entities by render priority if needed (background to foreground)
        // For now, render in order provided
        // For each entity in list:
        for (Entity entity : entities) {
            if (entity != null && entity.isVisible()) {
                // Call renderEntity() for the entity
                renderEntity(entity);
                // Handle entity-specific rendering effects
                // Update entitiesRendered counter
                this.entitiesRendered++;
            }
        }
    }

    /**
     * Renders with interpolation between two frames.
     * Used for smooth animation at different frame rates.
     *
     * @param world    Current world state
     * @param entities Current entity states
     * @param alpha    Interpolation factor (0.0 to 1.0)
     */
    public void renderWithInterpolation(TETile[][] world, List<Entity> entities, double alpha) {
        // Set this.interpolationAlpha = alpha
        double previousAlpha = this.interpolationAlpha;
        boolean previousInterpolation = this.entityInterpolationEnabled;

        this.interpolationAlpha = Math.max(0.0, Math.min(1.0, alpha));
        // Enable entity interpolation
        this.entityInterpolationEnabled = true;

        // Render world normally (world doesn't interpolate)
        renderWorldWithOffset(world);

        // For each entity: Calculate interpolated position based on alpha and render entity at interpolated position
        if (entities != null) {
            this.entitiesRendered = 0;
            for (Entity entity : entities) {
                if (entity != null && entity.isVisible()) {
                    renderEntity(entity);
                    this.entitiesRendered++;
                }
            }
        }

        // Reset interpolation settings after rendering
        this.interpolationAlpha = previousAlpha;
        this.entityInterpolationEnabled = previousInterpolation;
    }

    /**
     * Calculates the interpolated position for an entity.
     *
     * @param entity The entity to calculate position for
     * @param alpha  Interpolation factor
     * @return Interpolated position
     */
    private Position calculateInterpolatedPosition(Entity entity, double alpha) {
        if (entity == null) {
            return null;
        }

        Position currentPos = entity.getPosition();
        if (currentPos == null) {
            return null;
        }

        // If entityInterpolationEnabled and alpha < 1.0: Calculate interpolated position
        if (entityInterpolationEnabled && alpha < 1.0) {
            // Get entity's current and previous positions
            double prevX = currentPos.getPreviousX();
            double prevY = currentPos.getPreviousY();
            double currX = currentPos.getX();
            double currY = currentPos.getY();

            // Calculate interpolated position: previous + alpha * (current - previous)
            double interpolatedX = prevX + alpha * (currX - prevX);
            double interpolatedY = prevY + alpha * (currY - prevY);

            // Return interpolated position
            return new Position(interpolatedX, interpolatedY);
        }

        // Otherwise return current position
        return currentPos;
    }

    /**
     * Converts world coordinates to screen coordinates with camera offset.
     *
     * @param worldX World X coordinate
     * @param worldY World Y coordinate
     * @return Array containing [screenX, screenY]
     */
    public double[] worldToScreen(double worldX, double worldY) {
        // Apply camera offset: screenX = worldX - cameraOffsetX
        double screenX = worldX - cameraOffsetX;
        // Apply camera offset: screenY = worldY - cameraOffsetY
        double screenY = worldY - cameraOffsetY;

        // Add any additional screen transformations
        // Return screen coordinates as array
        return new double[]{screenX, screenY};
    }

    /**
     * Converts screen coordinates to world coordinates with camera offset.
     *
     * @param screenX Screen X coordinate
     * @param screenY Screen Y coordinate
     * @return Array containing [worldX, worldY]
     */
    public double[] screenToWorld(double screenX, double screenY) {
        // Reverse camera offset: worldX = screenX + cameraOffsetX
        double worldX = screenX + cameraOffsetX;
        // Reverse camera offset: worldY = screenY + cameraOffsetY
        double worldY = screenY + cameraOffsetY;

        // Return world coordinates as array
        return new double[]{worldX, worldY};
    }

    /**
     * Renders a tile at a specific sub-tile position.
     *
     * @param tile The tile to render
     * @param x    X position (can be fractional)
     * @param y    Y position (can be fractional)
     */
    public void renderTileAtPosition(TETile tile, double x, double y) {
        if (tile == null) {
            return;
        }

        // Calculate screen coordinates with worldToScreen()
        double[] screenCoords = worldToScreen(x, y);
        double screenX = screenCoords[0];
        double screenY = screenCoords[1];

        // Apply smooth positioning offset
        // Call tile.draw() with calculated position
        tile.draw(screenX, screenY);

        // Handle fractional positioning for smooth movement
        if (enableEffects) {
            applyTileEffects(tile, screenX, screenY);
        }
    }

    /**
     * Adds visual effects to tile rendering.
     *
     * @param tile The tile being rendered
     * @param x    Screen X position
     * @param y    Screen Y position
     */
    private void applyTileEffects(TETile tile, double x, double y) {
        // If not enableEffects, return early
        if (!enableEffects) {
            return;
        }

        // Check tile type for specific effects:
        // Power pellets: pulsing glow effect
        // Walls: subtle texture or shadow
        // Floor: ambient effects
        // Use effectTime for animated effects
        // Apply effects without modifying original tile

        // Basic implementation - can be expanded based on tile types
        // This is a placeholder for visual effects that would be implemented
        // based on specific tile characteristics and game requirements
    }

    /**
     * Updates effect timing for animated visual effects.
     *
     * @param deltaTime Time elapsed since last update
     */
    public void updateEffects(double deltaTime) {
        // Add deltaTime to effectTime
        this.effectTime += deltaTime;

        // Handle effect cycling (reset when reaching maximum)
        if (this.effectTime > 2 * Math.PI) { // Full cycle for trigonometric effects
            this.effectTime -= 2 * Math.PI;
        }

        // Update any time-based effect parameters
        // This provides a continuous time value for animated effects
    }

    /**
     * Enables or disables smooth rendering features.
     *
     * @param enabled true to enable smooth rendering
     */
    public void setSmoothRenderingEnabled(boolean enabled) {
        // Set this.smoothRenderingEnabled = enabled
        this.smoothRenderingEnabled = enabled;

        // If disabling, reset any smooth rendering state
        if (!enabled) {
            this.interpolationAlpha = 1.0;
        }

        // Update rendering behavior accordingly
    }

    /**
     * Enables or disables entity interpolation.
     *
     * @param enabled true to enable entity interpolation
     */
    public void setEntityInterpolationEnabled(boolean enabled) {
        // Set this.entityInterpolationEnabled = enabled
        this.entityInterpolationEnabled = enabled;

        // Reset interpolation alpha if disabling
        if (!enabled) {
            this.interpolationAlpha = 1.0;
        }
    }

    /**
     * Enables or disables visual effects.
     *
     * @param enabled true to enable effects
     */
    public void setEffectsEnabled(boolean enabled) {
        // Set this.enableEffects = enabled
        this.enableEffects = enabled;

        // Reset effect timing if disabling
        if (!enabled) {
            this.effectTime = 0.0;
        }
    }

    /**
     * Gets the current camera offset.
     *
     * @return Array containing [offsetX, offsetY]
     */
    public double[] getCameraOffset() {
        return new double[]{cameraOffsetX, cameraOffsetY};
    }

    /**
     * Checks if smooth rendering is enabled.
     *
     * @return true if smooth rendering is enabled
     */
    public boolean isSmoothRenderingEnabled() {
        return smoothRenderingEnabled;
    }

    /**
     * Checks if entity interpolation is enabled.
     *
     * @return true if entity interpolation is enabled
     */
    public boolean isEntityInterpolationEnabled() {
        return entityInterpolationEnabled;
    }

    /**
     * Checks if visual effects are enabled.
     *
     * @return true if effects are enabled
     */
    public boolean isEffectsEnabled() {
        return enableEffects;
    }

    /**
     * Sets the list of entities to render.
     *
     * @param entities The list of entities to render
     */
    public void setEntitiesToRender(List<Entity> entities) {
        this.entitiesToRender = entities;
    }

    /**
     * Gets rendering performance statistics.
     *
     * @return Map containing rendering metrics
     */
    public Map<String, Object> getRenderingStats() {
        // Create map with rendering metrics:
        Map<String, Object> stats = new HashMap<>();

        // tilesRendered, entitiesRendered
        stats.put("tilesRendered", tilesRendered);
        stats.put("entitiesRendered", entitiesRendered);

        // lastRenderTime, frame rate estimates
        stats.put("lastRenderTime", lastRenderTime);
        if (lastRenderTime > 0) {
            stats.put("estimatedFPS", 1000.0 / lastRenderTime);
        } else {
            stats.put("estimatedFPS", 0.0);
        }

        // Rendering feature states
        stats.put("smoothRenderingEnabled", smoothRenderingEnabled);
        stats.put("entityInterpolationEnabled", entityInterpolationEnabled);
        stats.put("effectsEnabled", enableEffects);

        // Performance indicators
        stats.put("cameraOffsetX", cameraOffsetX);
        stats.put("cameraOffsetY", cameraOffsetY);
        stats.put("interpolationAlpha", interpolationAlpha);
        stats.put("effectTime", effectTime);

        // Return statistics map
        return stats;
    }

    /**
     * Resets rendering statistics.
     */
    public void resetStats() {
        // Set tilesRendered = 0, entitiesRendered = 0
        this.tilesRendered = 0;
        this.entitiesRendered = 0;

        // Reset timing information
        this.lastRenderTime = 0;

        // Clear performance counters
        // Additional performance counters would be reset here if they existed
    }
}