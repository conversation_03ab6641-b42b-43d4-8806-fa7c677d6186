package core;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import tileengine.TETile;
import entities.Ghost;

/**
 * Abstract base class for all world types in the Pac-Man game.
 * Provides common functionality for world generation, entity management, and updates.
 * 
 * World Types:
 * - HubWorld: Fixed-size world for level selection and navigation
 * - LevelWorld: Infinite scrolling world for active Pac-Man gameplay
 */
public abstract class World {
    protected int width;
    protected int height;
    protected Random rand;
    
    // Common world elements
    protected List<Room> rooms;
    protected List<Ghost> ghosts;
    
    /**
     * Creates a world with specified dimensions and random seed.
     *
     * @param width Width of the world
     * @param height Height of the world  
     * @param seed Random seed for world generation
     */
    public World(int width, int height, long seed) {
        this.width = width;
        this.height = height;
        this.rand = new Random(seed);
        this.rooms = new ArrayList<>();
        this.ghosts = new ArrayList<>();
    }

    /**
     * Generates the world content into the provided tile array.
     * Implementation varies by world type.
     *
     * @param world The tile array to populate
     */
    public abstract void generateWorld(TETile[][] world);
    
    /**
     * Updates the world state including entities and dynamic elements.
     * Implementation varies by world type.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    public abstract void updateWorld(double deltaTime);
    
    /**
     * Gets the type of this world.
     *
     * @return WorldType enum value
     */
    public abstract WorldType getWorldType();

    /**
     * Gets the list of rooms in this world.
     *
     * @return List of rooms (copy to prevent external modification)
     */
    public List<Room> getRooms() {
        return new ArrayList<>(rooms);
    }
    
    /**
     * Gets the list of ghosts in this world.
     *
     * @return List of ghosts (copy to prevent external modification)
     */
    public List<Ghost> getGhosts() {
        return new ArrayList<>(ghosts);
    }
    
    /**
     * Gets the world dimensions.
     *
     * @return Array containing [width, height]
     */
    public int[] getDimensions() {
        return new int[]{width, height};
    }
    
    /**
     * Gets the random number generator for this world.
     *
     * @return Random instance used for world generation
     */
    public Random getRandom() {
        return rand;
    }
}

