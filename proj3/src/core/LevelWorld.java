package core;

import tileengine.TETile;
import tileengine.Tileset;
import components.Position;
import entities.Ghost;
import managers.WorldManager;
import core.Level.GameMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Level world implementation for active Pac-Man gameplay.
 * Extends World to provide infinite scrolling, active ghost AI, and gameplay mechanics.
 * 
 * This class provides:
 * - Infinite vertical scrolling world generation
 * - Active ghost AI with chase/fear behaviors
 * - Pellet spawning and collection mechanics
 * - Power pellet effects and ghost fear states
 * - Level-specific objectives and scoring
 * - Integration with chunk-based WorldManager
 */
public class LevelWorld extends World {
    
    // Level configuration
    private int levelId;
    private GameMode gameMode;
    private int targetScore;
    private double surviveTime;
    
    // World generation settings
    private WorldManager worldManager;
    private int chunkSize;
    private double pelletDensity;
    private double powerPelletDensity;
    
    // Gameplay state
    private boolean isActive;
    private double levelTime;
    private int pelletsCollected;
    private int powerPelletsCollected;
    private int currentScore;
    
    // Entity management
    private int activeGhostCount;
    private double ghostSpeedMultiplier;
    private double fearTimeMultiplier;
    
    /**
     * Creates a LevelWorld for active Pac-Man gameplay.
     * 
     * @param width Width of the visible world area
     * @param height Height of the visible world area
     * @param seed Random seed for world generation
     * @param levelId ID of this level
     * @param gameMode Game mode for this level
     */
    public LevelWorld(int width, int height, long seed, int levelId, GameMode gameMode) {
        super(width, height, seed);
        this.levelId = levelId;
        this.gameMode = gameMode;
        this.chunkSize = 50; // Default chunk size
        this.pelletDensity = 0.3; // 30% of floor tiles have pellets
        this.powerPelletDensity = 0.05; // 5% of pellets are power pellets
        this.isActive = false;
        this.levelTime = 0.0;
        this.pelletsCollected = 0;
        this.powerPelletsCollected = 0;
        this.currentScore = 0;
        this.activeGhostCount = 3 + levelId; // More ghosts in higher levels
        this.ghostSpeedMultiplier = 1.0 + (levelId * 0.1); // Faster ghosts in higher levels
        this.fearTimeMultiplier = 1.0 - (levelId * 0.05); // Shorter fear time in higher levels
        
        // Initialize objectives based on game mode
        initializeLevelObjectives();
    }
    
    /**
     * Alternative constructor with WorldManager integration.
     */
    public LevelWorld(int width, int height, long seed, int levelId, GameMode gameMode, WorldManager worldManager) {
        this(width, height, seed, levelId, gameMode);
        this.worldManager = worldManager;
    }
    
    @Override
    public void generateWorld(TETile[][] world) {
        // Initialize world with NOTHING tiles
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                world[x][y] = Tileset.NOTHING;
            }
        }
        
        // Generate infinite scrolling level world
        if (worldManager != null) {
            // Use WorldManager for chunk-based generation
            generateChunkedWorld(world);
        } else {
            // Generate simple vertical scrolling maze
            generateScrollingMaze(world);
        }
        
        // Spawn active ghosts
        spawnActiveGhosts();
        
        // Place pellets and power pellets
        placePellets(world);
        
        System.out.println("Generated level world " + levelId + " (" + gameMode + ")");
        System.out.println("Active ghosts: " + activeGhostCount);
        System.out.println("Target: " + getObjectiveDescription());
    }
    
    @Override
    public void updateWorld(double deltaTime) {
        if (!isActive) return;
        
        levelTime += deltaTime;
        
        // Update active ghosts with chase behavior
        for (Ghost ghost : ghosts) {
            // Set ghosts to active chase mode
            ghost.update(deltaTime);
        }
        
        // Check level completion
        checkLevelCompletion();
        
        // Update world chunks if using WorldManager
        if (worldManager != null) {
            // worldManager.updateChunks();
        }
    }
    
    @Override
    public WorldType getWorldType() {
        return WorldType.LEVEL_WORLD;
    }
    
    // ================================
    // LEVEL-SPECIFIC GENERATION METHODS
    // ================================
    
    private void generateChunkedWorld(TETile[][] world) {
        // TODO: Integrate with WorldManager for chunk-based generation
        // This will provide true infinite scrolling
        generateScrollingMaze(world); // Fallback for now
    }
    
    private void generateScrollingMaze(TETile[][] world) {
        // Generate a simple vertical maze for scrolling gameplay
        // Create corridors and obstacles suitable for Pac-Man gameplay
        
        // Create main vertical corridors
        for (int x = 5; x < width - 5; x += 10) {
            for (int y = 1; y < height - 1; y++) {
                world[x][y] = Tileset.FLOOR;
                // Add walls on sides
                if (x > 0) world[x - 1][y] = Tileset.WALL;
                if (x < width - 1) world[x + 1][y] = Tileset.WALL;
            }
        }
        
        // Add horizontal connections
        for (int y = 10; y < height; y += 15) {
            for (int x = 1; x < width - 1; x++) {
                if (world[x][y] == Tileset.NOTHING) {
                    world[x][y] = Tileset.FLOOR;
                    // Add walls above and below
                    if (y > 0) world[x][y - 1] = Tileset.WALL;
                    if (y < height - 1) world[x][y + 1] = Tileset.WALL;
                }
            }
        }
        
        // Fill remaining nothing tiles with walls for better maze structure
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                if (world[x][y] == Tileset.NOTHING) {
                    world[x][y] = Tileset.WALL;
                }
            }
        }
    }
    
    private void spawnActiveGhosts() {
        // Spawn ghosts with active chase AI
        for (int i = 0; i < activeGhostCount; i++) {
            // Find a suitable spawn position
            Position spawnPos = findGhostSpawnPosition();
            if (spawnPos != null) {
                // TODO: Create Ghost with chase behavior when Ghost class is available
                // Ghost activeGhost = new Ghost(spawnPos, GhostMode.CHASE);
                // activeGhost.setSpeedMultiplier(ghostSpeedMultiplier);
                // ghosts.add(activeGhost);
            }
        }
    }
    
    private Position findGhostSpawnPosition() {
        // Find a safe spawn position for ghosts
        // Should be away from player spawn and on floor tiles
        for (int attempt = 0; attempt < 20; attempt++) {
            int x = rand.nextInt(width);
            int y = rand.nextInt(height);
            // For now, return a random position
            // TODO: Add proper floor tile checking when world is generated
            return new Position(x, y);
        }
        return new Position(width / 2, height / 2); // Fallback to center
    }
    
    private void placePellets(TETile[][] world) {
        // Place pellets on floor tiles throughout the level
        // TODO: Implement pellet placement logic
        // - Iterate through world tiles
        // - Place regular pellets on floor tiles based on pelletDensity
        // - Place power pellets based on powerPelletDensity
        // - Ensure adequate spacing between power pellets
    }
    
    // ========================
    // LEVEL OBJECTIVE METHODS
    // ========================
    
    private void initializeLevelObjectives() {
        switch (gameMode) {
            case SURVIVE:
                surviveTime = 60.0 + (levelId * 15.0); // 60s base + 15s per level
                break;
            case EAT_PELLETS:
                targetScore = 100 + (levelId * 50); // 100 base + 50 per level
                break;
            case SCORE_TARGET:
                targetScore = 500 + (levelId * 200); // 500 base + 200 per level
                break;
        }
    }
    
    private void checkLevelCompletion() {
        boolean completed = false;
        
        switch (gameMode) {
            case SURVIVE:
                completed = (levelTime >= surviveTime);
                break;
            case EAT_PELLETS:
                completed = (pelletsCollected >= targetScore);
                break;
            case SCORE_TARGET:
                completed = (currentScore >= targetScore);
                break;
        }
        
        if (completed) {
            completeLevel();
        }
    }
    
    private void completeLevel() {
        isActive = false;
        System.out.println("Level " + levelId + " completed!");
        System.out.println("Time: " + String.format("%.1f", levelTime) + "s");
        System.out.println("Score: " + currentScore);
        // TODO: Trigger level completion event to transition back to hub world
    }
    
    private String getObjectiveDescription() {
        switch (gameMode) {
            case SURVIVE:
                return "Survive " + surviveTime + " seconds";
            case EAT_PELLETS:
                return "Eat " + targetScore + " pellets";
            case SCORE_TARGET:
                return "Reach " + targetScore + " points";
            default:
                return "Unknown objective";
        }
    }
    
    // ===================
    // GETTERS AND SETTERS
    // ===================
    
    public void setActive(boolean active) {
        this.isActive = active;
        if (active) {
            levelTime = 0.0; // Reset timer when level starts
        }
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public int getLevelId() {
        return levelId;
    }
    
    public GameMode getGameMode() {
        return gameMode;
    }
    
    public double getLevelTime() {
        return levelTime;
    }
    
    public int getCurrentScore() {
        return currentScore;
    }
    
    public void addScore(int points) {
        currentScore += points;
    }
    
    public void collectPellet() {
        pelletsCollected++;
        addScore(10); // Base pellet score
    }
    
    public void collectPowerPellet() {
        powerPelletsCollected++;
        addScore(50); // Power pellet score
        // TODO: Trigger ghost fear mode
    }
    
    public double getCompletionProgress() {
        switch (gameMode) {
            case SURVIVE:
                return Math.min(1.0, levelTime / surviveTime);
            case EAT_PELLETS:
                return Math.min(1.0, (double) pelletsCollected / targetScore);
            case SCORE_TARGET:
                return Math.min(1.0, (double) currentScore / targetScore);
            default:
                return 0.0;
        }
    }
    
    public void setWorldManager(WorldManager worldManager) {
        this.worldManager = worldManager;
    }
    
    /**
     * Gets debug information about the level state.
     */
    public String getDebugInfo() {
        return String.format("Level %d (%s) - Time: %.1fs, Score: %d, Progress: %.1f%%",
                levelId, gameMode, levelTime, currentScore, getCompletionProgress() * 100);
    }
}