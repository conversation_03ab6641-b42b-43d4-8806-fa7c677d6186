package core;

/**
 * Enumeration of different world types in the Pac-Man game.
 * Defines the behavior and characteristics of different world environments.
 */
public enum WorldType {
    /**
     * Hub world for level selection and navigation.
     * Features:
     * - Fixed size world with room-based layout
     * - Player navigation between rooms
     * - Level entrance doors in designated rooms
     * - Idle/decorative ghost behavior
     * - No active gameplay mechanics (scoring, pellets, etc.)
     */
    HUB_WORLD("Hub World"),

    /**
     * Level world for active Pac-Man gameplay.
     * Features:
     * - Infinite vertical scrolling world
     * - Active ghost AI and chase behavior
     * - Pellet collection and scoring mechanics
     * - Power pellets and ghost fear states
     * - Level-specific objectives and completion criteria
     */
    LEVEL_WORLD("Level World");

    private final String displayName;

    /**
     * Creates a WorldType with the specified display name.
     *
     * @param displayName Human-readable name for the world type
     */
    WorldType(String displayName) {
        this.displayName = displayName;
    }

    /**
     * Gets the human-readable display name for this world type.
     *
     * @return Display name of the world type
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Determines if this world type supports infinite scrolling.
     *
     * @return true if world supports infinite scrolling, false for fixed size
     */
    public boolean isInfiniteScrolling() {
        return this == LEVEL_WORLD;
    }

    /**
     * Determines if this world type has active gameplay mechanics.
     *
     * @return true if world has active gameplay (scoring, AI, etc.)
     */
    public boolean hasActiveGameplay() {
        return this == LEVEL_WORLD;
    }

    /**
     * Determines if ghosts in this world type should be in idle mode.
     *
     * @return true if ghosts should be idle/decorative
     */
    public boolean hasIdleGhosts() {
        return this == HUB_WORLD;
    }
}