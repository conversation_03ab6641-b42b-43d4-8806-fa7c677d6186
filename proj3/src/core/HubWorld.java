package core;

import tileengine.TETile;
import tileengine.Tileset;
import components.Position;
import managers.LevelManager;
import entities.Ghost;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

/**
 * Hub world implementation for level selection and navigation.
 * Extends World to reuse generation logic while adding level-specific functionality.
 * 
 * This class provides:
 * - Room-based world generation using existing World.generateWorld()
 * - Designation of specific rooms as level entrance points
 * - Door tile placement and visual feedback system
 * - Integration with level unlocking progression
 * - Player spawn point management for hub navigation
 */
public class HubWorld extends World {
    
    // Hub configuration constants
    private static final int MIN_LEVEL_ROOMS = 5;
    private static final int MAX_LEVEL_ROOMS = 8;
    private static final int DOOR_PLACEMENT_ATTEMPTS = 10;
    private static final int MIN_ROOM_SIZE_FOR_DOOR = 8; // Minimum room area to place door
    private static final int DOOR_BORDER_OFFSET = 2; // Distance from room walls
    
    // Level room management
    private List<Room> levelRooms;           // Rooms designated for level entrances
    private Map<Integer, Room> levelRoomMap; // Maps level ID to room
    private Map<Room, Position> doorPositions; // Door positions within rooms
    private Map<Integer, Position> levelDoorPositions; // Maps level ID to door position
    
    // Hub state
    private LevelManager levelManager;
    private Position playerSpawnPosition;
    private int numLevelRooms;
    
    // Door interaction
    private static final double DOOR_INTERACTION_RANGE = 1.5; // Distance for interaction
    private Map<Position, Integer> doorToLevelMap; // Maps door position to level ID
    
    /**
     * Creates a HubWorld with specified dimensions, seed, and level manager integration.
     * 
     * @param width Width of the hub world
     * @param height Height of the hub world  
     * @param seed Random seed for world generation
     * @param levelManager Reference to level management system
     */
    public HubWorld(int width, int height, long seed, LevelManager levelManager) {
        super(width, height, seed);
        this.levelManager = levelManager;
        this.levelRooms = new ArrayList<>();
        this.levelRoomMap = new HashMap<>();
        this.doorPositions = new HashMap<>();
        this.levelDoorPositions = new HashMap<>();
        this.doorToLevelMap = new HashMap<>();
        this.numLevelRooms = MIN_LEVEL_ROOMS + rand.nextInt(MAX_LEVEL_ROOMS - MIN_LEVEL_ROOMS + 1);
        this.playerSpawnPosition = null;
    }
    
    /**
     * Generates the hub world with designated level rooms and door placement.
     * Extends base world generation to add hub-specific functionality.
     * 
     * @param world The world tile array to populate
     */
    @Override
    public void generateWorld(TETile[][] world) {
        // Initialize world with NOTHING tiles
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                world[x][y] = Tileset.NOTHING;
            }
        }
        
        // Generate room-based world structure
        generateRandomRooms(world, rooms, rand, 15, 80);
        
        // Connect rooms with MST-based hallways
        connectAllRoomsWithMST(world, rooms);
        
        // Hub-specific functionality
        designateLevelRooms();
        placeDoorTiles(world);
        setPlayerSpawnPosition();
        initializeDoorToLevelMapping();
        
        // Add decorative ghosts in idle mode
        spawnIdleGhosts();
        
        // Print generation stats
        int totalArea = width * height;
        int roomArea = calculateRoomArea(rooms);
        double coverage = (double) roomArea / totalArea * 100;
        System.out.println("Generated hub world with " + rooms.size() + " rooms");
        System.out.println("Room coverage: " + String.format("%.1f", coverage) + "%");
        System.out.println("Level rooms: " + numLevelRooms);
    }
    
    /**
     * Designates specific rooms as level entrance points.
     * Selects rooms based on size, accessibility, and distribution.
     */
    private void designateLevelRooms() {
        // Filter rooms by minimum size
        List<Room> suitableRooms = new ArrayList<>();
        for (Room room : rooms) {
            if (room.width * room.height >= MIN_ROOM_SIZE_FOR_DOOR) {
                suitableRooms.add(room);
            }
        }
        
        // If not enough suitable rooms, use all available rooms
        if (suitableRooms.size() < numLevelRooms) {
            numLevelRooms = Math.min(numLevelRooms, suitableRooms.size());
        }
        
        // Select rooms with good distribution
        levelRooms.clear();
        levelRoomMap.clear();
        
        for (int levelId = 0; levelId < numLevelRooms && !suitableRooms.isEmpty(); levelId++) {
            Room bestRoom = null;
            double bestScore = -1;
            
            // Find room with best distribution score
            for (Room candidate : suitableRooms) {
                double score = calculateRoomDistributionScore(candidate, levelRooms);
                if (score > bestScore) {
                    bestScore = score;
                    bestRoom = candidate;
                }
            }
            
            if (bestRoom != null) {
                levelRooms.add(bestRoom);
                levelRoomMap.put(levelId, bestRoom);
                suitableRooms.remove(bestRoom);
            }
        }
        
        System.out.println("Designated " + levelRooms.size() + " rooms for level entrances");
    }
    
    /**
     * Places door tiles in designated level rooms.
     * Finds suitable positions within rooms and adds appropriate door tiles.
     * 
     * @param world The world tile array to modify
     */
    private void placeDoorTiles(TETile[][] world) {
        doorPositions.clear();
        levelDoorPositions.clear();
        
        for (int levelId = 0; levelId < levelRooms.size(); levelId++) {
            Room room = levelRooms.get(levelId);
            Position doorPos = findDoorPosition(room, world);
            
            if (doorPos != null) {
                // Place door tile (TODO: Use actual door tiles when available)
                world[(int) doorPos.getX()][(int) doorPos.getY()] = Tileset.LOCKED_DOOR;
                
                // Store door position mappings
                doorPositions.put(room, doorPos);
                levelDoorPositions.put(levelId, doorPos);
                
                System.out.println("Placed door for level " + levelId + " at " + doorPos);
            } else {
                System.out.println("Warning: Could not place door for level " + levelId + " in room at (" + room.x + "," + room.y + ")");
            }
        }
    }
    
    /**
     * Finds a suitable position for a door within a room.
     * Considers room layout, accessibility, and visual appeal.
     * 
     * @param room The room to place a door in
     * @param world The world tile array for collision checking
     * @return Position for door placement, or null if no suitable position found
     */
    private Position findDoorPosition(Room room, TETile[][] world) {
        // Try center first
        Position center = new Position(room.centerX(), room.centerY());
        if (isValidDoorPosition(center, room, world)) {
            return center;
        }
        
        // Try positions near center in expanding circles
        for (int radius = 1; radius <= 3; radius++) {
            for (int dx = -radius; dx <= radius; dx++) {
                for (int dy = -radius; dy <= radius; dy++) {
                    if (Math.abs(dx) == radius || Math.abs(dy) == radius) { // Only check perimeter
                        Position pos = new Position(room.centerX() + dx, room.centerY() + dy);
                        if (isValidDoorPosition(pos, room, world)) {
                            return pos;
                        }
                    }
                }
            }
        }
        
        return null; // No suitable position found
    }
    
    /**
     * Checks if a position is valid for door placement.
     * Validates tile type, accessibility, and spacing requirements.
     * 
     * @param position Position to check
     * @param room Room containing the position
     * @param world World tile array for validation
     * @return true if position is suitable for door placement
     */
    private boolean isValidDoorPosition(Position position, Room room, TETile[][] world) {
        int x = (int) position.getX();
        int y = (int) position.getY();
        
        // Check bounds
        if (x < 0 || x >= width || y < 0 || y >= height) {
            return false;
        }
        
        // Check if position is within room boundaries with border offset
        if (x <= room.x + DOOR_BORDER_OFFSET || x >= room.x + room.width - DOOR_BORDER_OFFSET ||
            y <= room.y + DOOR_BORDER_OFFSET || y >= room.y + room.height - DOOR_BORDER_OFFSET) {
            return false;
        }
        
        // Check if position is on floor tile
        if (world[x][y] != Tileset.FLOOR) {
            return false;
        }
        
        // Check if position doesn't conflict with existing doors
        for (Position existingDoor : doorPositions.values()) {
            if (existingDoor.equals(position)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Updates door tile appearances based on current level unlock status.
     * Called when level progression changes to refresh visual feedback.
     * 
     * @param world The world tile array to update
     */
    public void updateDoorTiles(TETile[][] world) {
        // TODO: Update door visuals based on unlock status
        // - Get unlocked levels set from levelManager
        // - Get completed levels set from levelManager  
        // - Iterate through levelDoorPositions map
        // - For each level door:
        //   - If level is completed: use LEVEL_DOOR_COMPLETED tile
        //   - If level is unlocked but not completed: use LEVEL_DOOR_UNLOCKED tile
        //   - If level is locked: use LEVEL_DOOR_LOCKED tile
        // - Update world tile array with appropriate door tile
        throw new UnsupportedOperationException("Door tile update implementation needed");
    }
    
    /**
     * Checks if player is within interaction range of any level door.
     * Used for input handling and UI feedback.
     * 
     * @param playerPosition Current player position
     * @return Level ID of nearby door, or -1 if no door in range
     */
    public int checkDoorInteraction(Position playerPosition) {
        // TODO: Check for door proximity
        // - Iterate through levelDoorPositions map
        // - Calculate distance from playerPosition to each door position
        // - If distance <= DOOR_INTERACTION_RANGE:
        //   - Return the level ID for that door
        // - If multiple doors in range, return closest one
        // - If no doors in range, return -1
        // - Consider using Euclidean distance for smooth interaction
        throw new UnsupportedOperationException("Door interaction check implementation needed");
    }
    
    /**
     * Gets the level ID associated with a door at the specified position.
     * Used for direct position-to-level mapping.
     * 
     * @param doorPosition Position of the door
     * @return Level ID for the door, or -1 if no door at position
     */
    public int getDoorAtPosition(Position doorPosition) {
        // TODO: Get level ID for door position
        // - Check doorToLevelMap for exact position match
        // - Return level ID if found
        // - Return -1 if no door at specified position
        // - Consider implementing fuzzy matching for near-exact positions
        throw new UnsupportedOperationException("Door position lookup implementation needed");
    }
    
    /**
     * Sets the player spawn position in the hub world.
     * Finds a suitable starting location that's accessible and central.
     */
    private void setPlayerSpawnPosition() {
        // Find largest non-level room for player spawn
        Room spawnRoom = null;
        int largestArea = 0;
        
        for (Room room : rooms) {
            if (!levelRooms.contains(room)) {
                int area = room.width * room.height;
                if (area > largestArea) {
                    largestArea = area;
                    spawnRoom = room;
                }
            }
        }
        
        // If no non-level rooms available, use first room
        if (spawnRoom == null && !rooms.isEmpty()) {
            spawnRoom = rooms.get(0);
        }
        
        if (spawnRoom != null) {
            playerSpawnPosition = new Position(spawnRoom.centerX(), spawnRoom.centerY());
            System.out.println("Player spawn position: " + playerSpawnPosition);
        } else {
            // Fallback to center of world
            playerSpawnPosition = new Position(width / 2, height / 2);
            System.out.println("Warning: Using fallback spawn position at world center");
        }
    }
    
    /**
     * Initializes the mapping from door positions to level IDs.
     * Sets up efficient lookup for door interaction system.
     */
    private void initializeDoorToLevelMapping() {
        doorToLevelMap.clear();
        
        for (Map.Entry<Integer, Position> entry : levelDoorPositions.entrySet()) {
            Integer levelId = entry.getKey();
            Position doorPos = entry.getValue();
            doorToLevelMap.put(doorPos, levelId);
        }
        
        System.out.println("Initialized door-to-level mapping for " + doorToLevelMap.size() + " doors");
    }
    
    /**
     * Handles level unlock progression by updating door visuals.
     * Called by LevelManager when a level is completed.
     * 
     * @param levelId ID of the level that was completed
     * @param world World tile array to update
     */
    public void onLevelCompleted(int levelId, TETile[][] world) {
        // TODO: Handle level completion
        // - Update door tile for completed level to LEVEL_DOOR_COMPLETED
        // - Check if next level should be unlocked
        // - Update next level's door tile if newly unlocked
        // - Refresh all door tiles to ensure consistency
        // - Consider adding visual effects or sound for completion
        throw new UnsupportedOperationException("Level completion handling implementation needed");
    }
    
    /**
     * Handles level unlock by updating door visuals.
     * Called by LevelManager when a new level becomes available.
     * 
     * @param levelId ID of the level that was unlocked
     * @param world World tile array to update
     */
    public void onLevelUnlocked(int levelId, TETile[][] world) {
        // TODO: Handle level unlock
        // - Update door tile for unlocked level to LEVEL_DOOR_UNLOCKED
        // - Ensure door position is accessible
        // - Refresh door tile in world array
        // - Consider adding visual feedback for unlock (particle effects, etc.)
        throw new UnsupportedOperationException("Level unlock handling implementation needed");
    }
    
    /**
     * Gets information about a specific level door.
     * Used for UI display and interaction feedback.
     * 
     * @param levelId ID of the level
     * @return Map containing door information (position, status, etc.)
     */
    public Map<String, Object> getLevelDoorInfo(int levelId) {
        // TODO: Get level door information
        // - Create map with door information:
        //   - "position": door position
        //   - "isUnlocked": unlock status from level manager
        //   - "isCompleted": completion status from level manager
        //   - "roomCenter": center of room containing door
        //   - "levelName": name/description of level
        // - Return information map for UI display
        // - Return null if level ID is invalid
        throw new UnsupportedOperationException("Level door info retrieval implementation needed");
    }
    
    /**
     * Validates hub world configuration and setup.
     * Checks that all level rooms have doors and are properly configured.
     * 
     * @return true if hub world is properly configured
     */
    public boolean validateHubWorld() {
        // TODO: Validate hub world setup
        // - Check that levelRooms list is not empty
        // - Verify all level rooms have corresponding door positions
        // - Ensure all door positions are valid and accessible
        // - Check that player spawn position is set and valid
        // - Validate level manager integration
        // - Return overall validation result
        throw new UnsupportedOperationException("Hub world validation implementation needed");
    }
    
    // Getter methods for integration and debugging
    
    /**
     * Gets the player spawn position in the hub world.
     * 
     * @return Position where player should spawn in hub
     */
    public Position getPlayerSpawnPosition() {
        return playerSpawnPosition;
    }
    
    /**
     * Gets the list of rooms designated for level entrances.
     * 
     * @return List of level rooms
     */
    public List<Room> getLevelRooms() {
        return new ArrayList<>(levelRooms); // Return copy to prevent external modification
    }
    
    /**
     * Gets the room associated with a specific level ID.
     * 
     * @param levelId ID of the level
     * @return Room containing the level door, or null if level ID invalid
     */
    public Room getLevelRoom(int levelId) {
        return levelRoomMap.get(levelId);
    }
    
    /**
     * Gets the door position for a specific level ID.
     * 
     * @param levelId ID of the level
     * @return Position of the level door, or null if level ID invalid
     */
    public Position getLevelDoorPosition(int levelId) {
        return levelDoorPositions.get(levelId);
    }
    
    /**
     * Gets the number of level rooms in the hub world.
     * 
     * @return Number of level entrances
     */
    public int getNumLevelRooms() {
        return numLevelRooms;
    }
    
    /**
     * Gets debug information about the hub world state.
     * Used for development and troubleshooting.
     * 
     * @return Map containing debug information
     */
    public Map<String, Object> getDebugInfo() {
        // TODO: Collect hub world debug information
        // - Create map with debug data:
        //   - Number of level rooms and their positions
        //   - Door positions and their associated level IDs
        //   - Player spawn position
        //   - Level manager integration status
        //   - Room generation statistics
        // - Return debug information map
        throw new UnsupportedOperationException("Hub world debug info collection implementation needed");
    }
    
    /**
     * Resets the hub world to initial state.
     * Used for world regeneration or game restart.
     */
    public void reset() {
        // TODO: Reset hub world state
        // - Clear all room and door mappings
        // - Reset player spawn position
        // - Clear level room designations
        // - Prepare for regeneration
        throw new UnsupportedOperationException("Hub world reset implementation needed");
    }
    
    @Override
    public void updateWorld(double deltaTime) {
        // Hub world has minimal updates - just idle ghost behavior
        for (Ghost ghost : ghosts) {
            // Set ghosts to idle/decorative mode
            ghost.update(deltaTime);
        }
    }
    
    @Override
    public WorldType getWorldType() {
        return WorldType.HUB_WORLD;
    }
    
    // ===============================
    // WORLD GENERATION HELPER METHODS
    // ===============================
    // Moved from abstract World class
    
    private void generateRandomRooms(TETile[][] world, List<Room> rooms, Random rand, int numRooms, int maxAttempts) {
        for (int i = 0; i < numRooms; i++) {
            for (int attempt = 0; attempt < maxAttempts; attempt++) {
                int roomWidth = 6 + rand.nextInt(13); // Rooms between 6 and 18 units wide
                int roomHeight = 6 + rand.nextInt(10); // Rooms between 6 and 15 units high

                int x = rand.nextInt(width - roomWidth - 1) + 1;
                int y = rand.nextInt(height - roomHeight - 1) + 1;

                Room newRoom = new Room(x, y, roomWidth, roomHeight);

                boolean overlaps = false;
                for (Room existing : rooms) {
                    if (newRoom.intersects(existing)) {
                        overlaps = true;
                        break;
                    }
                }

                if (!overlaps) {
                    addRoom(world, newRoom);
                    rooms.add(newRoom);
                    break; // Successfully placed room
                }
            }
        }
    }

    private int calculateRoomArea(List<Room> rooms) {
        int totalArea = 0;
        for (Room room : rooms) {
            totalArea += room.width * room.height;
        }
        return totalArea;
    }

    private void addRoom(TETile[][] world, Room r) {
        for (int x = r.x; x < r.x + r.width; x++) {
            for (int y = r.y; y < r.y + r.height; y++) {
                if (x == r.x || x == r.x + r.width - 1 || y == r.y || y == r.y + r.height - 1) {
                    world[x][y] = Tileset.WALL;
                } else {
                    world[x][y] = Tileset.FLOOR;
                }
            }
        }
    }

    private void connectAllRoomsWithMST(TETile[][] world, List<Room> rooms) {
        List<Edge> edges = new ArrayList<>();

        for (int i = 0; i < rooms.size(); i++) {
            for (int j = i + 1; j < rooms.size(); j++) {
                double dist = distance(rooms.get(i), rooms.get(j));
                edges.add(new Edge(i, j, dist));
            }
        }

        UnionFind uf = new UnionFind(rooms.size());

        for (Edge e : edges) {
            if (uf.union(e.roomA, e.roomB)) {
                connectRooms(world, rooms.get(e.roomA), rooms.get(e.roomB));
            }
        }
    }

    private double distance(Room r1, Room r2) {
        int x1 = r1.centerX(), y1 = r1.centerY();
        int x2 = r2.centerX(), y2 = r2.centerY();
        return Math.hypot(x1 - x2, y1 - y2);
    }

    private void connectRooms(TETile[][] world, Room r1, Room r2) {
        int x1 = r1.centerX(), y1 = r1.centerY();
        int x2 = r2.centerX(), y2 = r2.centerY();

        // Drunken walk for hallway generation
        int currentX = x1;
        int currentY = y1;

        while (currentX != x2 || currentY != y2) {
            boolean moveX = (currentX != x2) && (rand.nextBoolean() || currentY == y2);

            if (moveX) {
                int nextX = currentX < x2 ? currentX + 1 : currentX - 1;
                drawHorizontalCorridor(world, currentX, nextX, currentY);
                currentX = nextX;
            } else {
                int nextY = currentY < y2 ? currentY + 1 : currentY - 1;
                drawVerticalCorridor(world, currentY, nextY, currentX);
                currentY = nextY;
            }
        }
    }

    private void drawHorizontalCorridor(TETile[][] world, int xStart, int xEnd, int y) {
        for (int x = Math.min(xStart, xEnd); x <= Math.max(xStart, xEnd); x++) {
            if (changeToFloorOrWall(world[x][y])) world[x][y] = Tileset.FLOOR;
            if (changeToFloorOrWall(world[x][y + 1])) world[x][y + 1] = Tileset.WALL;
            if (changeToFloorOrWall(world[x][y - 1])) world[x][y - 1] = Tileset.WALL;
        }
    }

    private void drawVerticalCorridor(TETile[][] world, int yStart, int yEnd, int x) {
        for (int y = Math.min(yStart, yEnd); y <= Math.max(yStart, yEnd); y++) {
            if (changeToFloorOrWall(world[x][y])) world[x][y] = Tileset.FLOOR;
            if (changeToFloorOrWall(world[x + 1][y])) world[x + 1][y] = Tileset.WALL;
            if (changeToFloorOrWall(world[x - 1][y])) world[x - 1][y] = Tileset.WALL;
        }
    }

    private boolean changeToFloorOrWall(TETile tile) {
        return tile == Tileset.NOTHING || tile == Tileset.WALL;
    }
    
    private void spawnIdleGhosts() {
        // Add a few decorative ghosts in idle mode
        // Place them in random rooms for visual appeal
        int numGhosts = Math.min(3, rooms.size() / 3); // Max 3 ghosts, 1 per 3 rooms
        
        for (int i = 0; i < numGhosts && i < rooms.size(); i++) {
            Room room = rooms.get(rand.nextInt(rooms.size()));
            Position ghostPos = new Position(room.centerX(), room.centerY());
            // TODO: Create Ghost with idle behavior when Ghost class is available
            // Ghost idleGhost = new Ghost(ghostPos, GhostMode.IDLE);
            // ghosts.add(idleGhost);
        }
    }
    
    /**
     * Calculates a distribution score for room selection.
     * Higher scores indicate better spatial distribution.
     */
    private double calculateRoomDistributionScore(Room candidate, List<Room> existingRooms) {
        if (existingRooms.isEmpty()) {
            return 100.0; // First room gets max score
        }
        
        double minDistance = Double.MAX_VALUE;
        double totalDistance = 0.0;
        
        for (Room existing : existingRooms) {
            double dist = distance(candidate, existing);
            minDistance = Math.min(minDistance, dist);
            totalDistance += dist;
        }
        
        // Score based on minimum distance to avoid clustering
        // and average distance for overall distribution
        return minDistance * 0.7 + (totalDistance / existingRooms.size()) * 0.3;
    }

    private static class Edge {
        int roomA, roomB;
        double weight;

        Edge(int a, int b, double w) {
            this.roomA = a;
            this.roomB = b;
            this.weight = w;
        }
    }

    private static class UnionFind {
        int[] parent;

        UnionFind(int n) {
            parent = new int[n];
            for (int i = 0; i < n; i++) parent[i] = i;
        }

        int find(int x) {
            if (parent[x] != x) parent[x] = find(parent[x]);
            return parent[x];
        }

        boolean union(int x, int y) {
            int rootX = find(x), rootY = find(y);
            if (rootX == rootY) return false;
            parent[rootX] = rootY;
            return true;
        }
    }
}