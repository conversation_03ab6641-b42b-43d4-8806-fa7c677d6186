package core;

/**
 * Centralized state management for the Pac-Man game.
 * Defines all possible game states and manages transitions between them.
 * <p>
 * Game State Flow:
 * START_MENU -> PLAYING -> PAUSED -> PLAYING
 * -> SETTINGS -> START_MENU
 * -> PLAYING -> GAME_OVER -> START_MENU
 */
public enum GameState {
    /**
     * Initial state when game launches.
     * Shows main menu with options: New Game, Load Game, Settings, Quit.
     */
    START_MENU("Main Menu"),

    /**
     * Active gameplay state.
     * Player can move, collect pellets, use skills, and interact with ghosts.
     */
    PLAYING("Playing"),

    /**
     * Game is temporarily halted.
     * Triggered by ESC key during PLAYING state.
     * Shows pause menu with options: Resume, Settings, Main Menu.
     */
    PAUSED("Paused"),

    /**
     * Terminal state when player dies or quits.
     * Shows final score, high score comparison, and options: Restart, Main Menu.
     */
    GAME_OVER("Game Over"),

    /**
     * Configuration state accessible from START_MENU or PAUSED.
     * Allows adjustment of: Audio volume, Theme selection, Controls.
     */
    SETTINGS("Settings"),

    /**
     * Hub world navigation state.
     * Player can move around hub world and select levels to enter.
     * Shows visual indicators of level unlock status and completion.
     */
    HUB_WORLD("Hub World"),

    /**
     * Active level gameplay state.
     * Player is playing within a specific level with defined objectives.
     * Similar to PLAYING but with level-specific goals and constraints.
     */
    LEVEL_PLAYING("Level Playing");

    // Human-readable name for the state
    private final String displayName;

    /**
     * Constructor for GameState enum.
     *
     * @param displayName Human-readable name for the state
     */
    GameState(String displayName) {
        this.displayName = displayName;
    }

    /**
     * Determines if a transition from this state to the target state is valid.
     * Prevents invalid state transitions that could break the game flow.
     * <p>
     * Valid transitions:
     * - START_MENU -> PLAYING, HUB_WORLD, SETTINGS
     * - PLAYING -> PAUSED, GAME_OVER (legacy single-level mode)
     * - HUB_WORLD -> LEVEL_PLAYING, PAUSED, START_MENU
     * - LEVEL_PLAYING -> HUB_WORLD, PAUSED, GAME_OVER
     * - PAUSED -> (previous state: PLAYING, HUB_WORLD, or LEVEL_PLAYING)
     * - GAME_OVER -> START_MENU, HUB_WORLD
     * - SETTINGS -> START_MENU, PAUSED (depending on origin)
     *
     * @param target The target state to transition to
     * @return true if the transition is allowed, false otherwise
     */
    public boolean canTransition(GameState target) {
        // TODO: Consider adding logging for invalid transition attempts

        if (this == START_MENU) {
            return target == PLAYING || target == HUB_WORLD || target == SETTINGS;
        } else if (this == PLAYING) {
            return target == PAUSED || target == GAME_OVER;
        } else if (this == HUB_WORLD) {
            return target == LEVEL_PLAYING || target == PAUSED || target == START_MENU;
        } else if (this == LEVEL_PLAYING) {
            return target == HUB_WORLD || target == PAUSED || target == GAME_OVER;
        } else if (this == PAUSED) {
            return target == PLAYING || target == HUB_WORLD || target == LEVEL_PLAYING || target == SETTINGS || target == START_MENU;
        } else if (this == GAME_OVER) {
            return target == START_MENU || target == HUB_WORLD;
        } else if (this == SETTINGS) {
            return target == START_MENU || target == PAUSED;
        } else {
            return false;
        }
    }

    /**
     * Checks if the current state allows gameplay updates.
     * Used by GameEngine to determine which systems should be updated.
     *
     * @return true if game logic should be updated, false otherwise
     */
    public boolean isGameplayActive() {
        // - Return true for PLAYING, HUB_WORLD, and LEVEL_PLAYING states
        // - Return false for all menu states and PAUSED
        // - This determines if entities should move, collisions should be checked, etc.
        // - Hub world has limited gameplay (player movement, door interaction)
        // - Level playing has full gameplay (ghosts, pellets, scoring)

        if (this == PLAYING || this == HUB_WORLD || this == LEVEL_PLAYING) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Checks if the current state allows rendering the game world.
     * Some states show menus over the game world, others hide it completely.
     *
     * @return true if the game world should be rendered, false otherwise
     */
    public boolean shouldRenderWorld() {
        // - Return true for PLAYING, HUB_WORLD, LEVEL_PLAYING and PAUSED (paused shows world with overlay)
        // - Return false for START_MENU, GAME_OVER, SETTINGS (full screen menus)
        // - This determines if the world tiles should be drawn
        // - Hub world and level playing both need world rendering

        if (this == PLAYING || this == HUB_WORLD || this == LEVEL_PLAYING || this == PAUSED) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Checks if the current state allows audio playback.
     * Used to pause/resume background music and sound effects.
     *
     * @return true if audio should be active, false otherwise
     */
    public boolean isAudioActive() {
        // TODO: Consider different audio categories (music vs sound effects)
        // - Return true for PLAYING (full audio)
        // - Return false for PAUSED (audio should be paused)
        // - Return true for menu states (menu music/sounds)

        if (this == PLAYING) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Gets a human-readable string representation of the state.
     * Used for debugging, logging, and potentially UI display.
     * <p>
     * - START_MENU -> "Main Menu"
     * - PLAYING -> "Playing"
     * - PAUSED -> "Paused"
     * - GAME_OVER -> "Game Over"
     * - SETTINGS -> "Settings"
     *
     * @return A descriptive string for the current state
     */
    public String getDisplayName() {
        return displayName;
    }
}