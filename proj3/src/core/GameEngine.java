package core;

import edu.princeton.cs.algs4.StdDraw;
import managers.AssetManager;
import managers.AudioManager;
import managers.EntityManager;
import managers.ScoreManager;
import managers.SkillManager;
import managers.WorldManager;
import rendering.SmoothRenderer;
import tileengine.TETile;
import ui.HUD;
import ui.MenuSystem;

/**
 * Main game loop coordinator and system orchestrator.
 * This class manages the overall game lifecycle, coordinates all game systems,
 * and handles the main game loop execution.
 * <p>
 * The GameEngine follows a standard game loop pattern:
 * 1. Handle Input
 * 2. Update Game State
 * 3. Render Frame
 * 4. Control Timing
 */
public class GameEngine {
    // Core game state
    private GameState gameState;
    private boolean isRunning;
    private long lastUpdateTime;
    private double deltaTime;

    // System managers
    private EntityManager entityManager;
    private AssetManager assetManager;
    private AudioManager audioManager;
    private WorldManager worldManager;
    private ScoreManager scoreManager;
    private SkillManager skillManager;

    // UI systems
    private MenuSystem menuSystem;
    private HUD hud;

    // Rendering systems
    private SmoothRenderer renderer;

    // Game world
    private TETile[][] currentWorld;
    private int worldWidth;
    private int worldHeight;

    /**
     * Constructs a new GameEngine with specified world dimensions.
     * Initializes all game systems but does not start the game loop.
     *
     * @param width  The width of the game world in tiles
     * @param height The height of the game world in tiles
     */
    public GameEngine(int width, int height) {
        // - Set world dimensions
        worldWidth = width;
        worldHeight = height;
        // - Initialize GameState to START_MENU
        gameState = GameState.START_MENU;
        // - Set isRunning to false
        isRunning = false;
        // - Initialize lastUpdateTime to current time
        lastUpdateTime = System.currentTimeMillis();
    }

    /**
     * Initializes all game systems and prepares for gameplay.
     * This method should be called once before starting the game loop.
     * <p>
     * Steps to implement:
     * 1. Initialize renderer with world dimensions
     * 2. Create and initialize all manager systems
     * 3. Initialize UI systems (menu, HUD)
     * 4. Load essential assets
     * 5. Set up initial game state
     * 6. Initialize world array
     */
    public void initialize() {
        // TODO: Initialize all game systems
        // - Create SmoothRenderer instance
        renderer = new SmoothRenderer();
        // - Initialize all manager systems
        entityManager = new EntityManager();
        assetManager = new AssetManager();
        audioManager = new AudioManager();

        scoreManager = new ScoreManager();
        skillManager = new SkillManager();
        // - Set up MenuSystem and HUD
        menuSystem = new MenuSystem();
        hud = new HUD();
        // - Load critical assets through AssetManager

        // - Initialize world array with proper dimensions
        worldManager = new WorldManager(0, worldWidth, worldHeight);
        // - Set initial game state        
        throw new UnsupportedOperationException("Initialize method implementation needed");
    }

    /**
     * Main game loop that runs until the game is terminated.
     * Follows the standard game loop pattern with fixed timestep.
     * <p>
     * Loop structure:
     * 1. Calculate delta time since last frame
     * 2. Handle user input based on current game state
     * 3. Update all game systems
     * 4. Render current frame
     * 5. Control frame rate timing
     * <p>
     * The loop continues until isRunning becomes false.
     */
    public void gameLoop() {
        // - Set isRunning to true
        isRunning = true;
        // - While isRunning is true:
        while (isRunning) {
            //   - Calculate deltaTime from lastUpdateTime
            deltaTime = (System.currentTimeMillis() - lastUpdateTime) / 1000.0;
            //   - Handle input based on current game state
            handleInput();
            //   - Update game systems based on game state
            update(deltaTime);
            //   - Render current frame
            render();
            //   - Control frame rate timing
            StdDraw.pause(16);
            //   - Update lastUpdateTime
            lastUpdateTime = System.currentTimeMillis();
        }
    }

    /**
     * Handles user input based on the current game state.
     * Delegates input handling to appropriate systems.
     * <p>
     * Input handling varies by game state:
     * - START_MENU: Menu navigation input
     * - PLAYING: Game controls (movement, skills)
     * - PAUSED: Resume/menu input
     * - GAME_OVER: Restart/menu input
     * - SETTINGS: Settings navigation input
     */
    private void handleInput() {
        // TODO: Implement input handling
        // - Check if StdDraw.hasNextKeyTyped()
        // - Get the key with StdDraw.nextKeyTyped()
        // - Switch on current game state:
        //   - START_MENU: delegate to menuSystem.handleMenuInput()
        //   - PLAYING: handle game controls (WASD, skills, pause)
        //   - PAUSED: handle resume/menu input
        //   - GAME_OVER: handle restart/menu input
        //   - SETTINGS: delegate to menuSystem.handleSettingsInput()
        // - Update game state transitions as needed

        if (StdDraw.hasNextKeyTyped()) {
            char key = StdDraw.nextKeyTyped();
            switch (gameState) {
                case START_MENU:
                    menuSystem.handleMenuInput(key);
                    break;
                case PLAYING:
                    // TODO: handleGameControls(key);
                    break;
                case PAUSED:
                    // TODO: handlePauseInput(key);
                    break;
                case GAME_OVER:
                    // TODO: handleGameOverInput(key);
                    break;
                case SETTINGS:
                    menuSystem.handleSettingsInput(key);
                    break;
            }
        }
    }

    /**
     * Updates all game systems for the current frame.
     * Only updates systems relevant to the current game state.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    private void update(double deltaTime) {
        // - Switch on current game state:
        //   - PLAYING: 
        //     - Update entityManager.updateAll(deltaTime)
        //     - Update skillManager.updateSkills(deltaTime)
        //     - Update worldManager if needed
        //     - Check for game over conditions
        //   - PAUSED: No game logic updates
        //   - Other states: Update only relevant systems

        switch (gameState) {
            case PLAYING:
                entityManager.updateAll(deltaTime);
                skillManager.updateSkills(deltaTime);
                worldManager.update(entityManager.getPlayer().getPosition());
                break;
            case PAUSED:
                break;
            case GAME_OVER:
                break;
            case SETTINGS:
                break;
            case START_MENU:
                break;
        }
    }

    /**
     * Renders the current frame based on game state.
     * Delegates rendering to appropriate systems.
     */
    private void render() {
        // TODO: Implement rendering
        // - Switch on current game state:
        //   - START_MENU: menuSystem.showStartMenu()
        //   - PLAYING: 
        //     - renderer.renderFrame(currentWorld)
        //     - hud.renderScore(), hud.renderSkillBar(), hud.renderHealth()
        //   - PAUSED: 
        //     - renderer.renderFrame(currentWorld) with overlay
        //     - menuSystem.showPauseMenu()
        //   - GAME_OVER: menuSystem.showGameOverMenu()
        //   - SETTINGS: menuSystem.showSettings()

        switch (gameState) {
            case START_MENU:
                menuSystem.showStartMenu();
                break;
            case PLAYING:
                renderer.renderFrame(currentWorld);
                hud.renderScore();
                hud.renderSkillBar();
                hud.renderHealth();
                break;
            case PAUSED:
                renderer.renderFrame(currentWorld);
                menuSystem.showPauseMenu();
                break;
            case GAME_OVER:
                menuSystem.showGameOverMenu();
                break;
            case SETTINGS:
                menuSystem.showSettings();
                break;
        }
        StdDraw.show();
    }

    /**
     * Transitions to a new game state.
     * Handles cleanup of old state and initialization of new state.
     *
     * @param newState The target game state
     */
    public void setState(GameState newState) {
        // - Check if newState is different from current state
        if (newState == gameState) {
            return;
        }

        // - Check if transition is valid using gameState.canTransition()
        if (gameState.canTransition(newState)) {
            // TODO: Handle cleanup for current state if needed
            // Do sth if needed
            // - Set gameState to newState
            gameState = newState;
            // TODO: Handle initialization for new state (e.g., loading menus, pausing audio)
        } else {
            System.out.println("Invalid state transition: " + gameState + " to " + newState);
        }
    }

    /**
     * Shuts down the game engine and cleans up resources.
     * Should be called when the application is terminating.
     */
    public void shutdown() {
        // TODO: Implement shutdown cleanup
        // - Set isRunning to false
        isRunning = false;
        // - Stop all audio through audioManager
        audioManager.shutdown();
        // - Save game state if in PLAYING mode
        if (gameState == GameState.PLAYING) {
            // TODO: saveGameState();
        }
        // - Clean up renderer resources
        // TODO: renderer.shutdown();
        // - Clean up any other system resources
        throw new UnsupportedOperationException("Shutdown implementation needed");
    }

    // Getters for system access
    public GameState getGameState() {
        return gameState;
    }

    public EntityManager getEntityManager() {
        return entityManager;
    }

    public ScoreManager getScoreManager() {
        return scoreManager;
    }

    public SkillManager getSkillManager() {
        return skillManager;
    }

    public boolean isRunning() {
        return isRunning;
    }
}