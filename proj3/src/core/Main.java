package core;

import config.GameConfig;

/**
 * Main class - Entry point for the Pac-Man game.
 * Coordinates initialization of all major game systems and starts the game engine.
 * <p>
 * This class provides:
 * - Game system initialization and coordination
 * - Command line argument processing
 * - Error handling and graceful shutdown
 * - Integration between GameEngine and all manager systems
 */
public class Main {
    // Default game configuration from GameConfig constants
    private static final int DEFAULT_WIDTH = GameConfig.DEFAULT_WORLD_WIDTH;
    private static final int DEFAULT_HEIGHT = GameConfig.DEFAULT_WORLD_HEIGHT;
    private static final long DEFAULT_SEED = 5022257891541781577L;

    // Core game system
    private static GameEngine gameEngine;

    /**
     * Main entry point for the Pac-Man game.
     * Processes command line arguments and initializes the game.
     *
     * @param args Command line arguments (optional: seed, width, height)
     */
    public static void main(String[] args) {
        try {
            // Process command line arguments
            ParsedConfig config = parseArguments(args);

            // Initialize game systems
            initializeGameSystems(config);

            // Start the game
            startGame();

        } catch (Exception e) {
            System.err.println("Fatal error during game initialization: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

    /**
     * Parses command line arguments into game configuration.
     * Supports optional seed, width, and height parameters.
     *
     * @param args Command line arguments
     * @return ParsedConfig with parsed settings
     */
    private static ParsedConfig parseArguments(String[] args) {
        long seed = DEFAULT_SEED;
        int width = DEFAULT_WIDTH;
        int height = DEFAULT_HEIGHT;
        boolean loadGame = false;
        String saveFile = null;

        // Parse arguments
        for (int i = 0; i < args.length; i++) {
            switch (args[i].toLowerCase()) {
                case "-seed":
                case "--seed":
                    if (i + 1 < args.length) {
                        try {
                            seed = Long.parseLong(args[++i]);
                        } catch (NumberFormatException e) {
                            System.err.println("Warning: Invalid seed format, using default");
                        }
                    }
                    break;
                case "-width":
                case "--width":
                    if (i + 1 < args.length) {
                        try {
                            width = Integer.parseInt(args[++i]);
                            width = Math.max(40, Math.min(width, 200)); // Clamp to reasonable range
                        } catch (NumberFormatException e) {
                            System.err.println("Warning: Invalid width format, using default");
                        }
                    }
                    break;
                case "-height":
                case "--height":
                    if (i + 1 < args.length) {
                        try {
                            height = Integer.parseInt(args[++i]);
                            height = Math.max(30, Math.min(height, 100)); // Clamp to reasonable range
                        } catch (NumberFormatException e) {
                            System.err.println("Warning: Invalid height format, using default");
                        }
                    }
                    break;
                case "-load":
                case "--load":
                    loadGame = true;
                    if (i + 1 < args.length && !args[i + 1].startsWith("-")) {
                        saveFile = args[++i];
                    }
                    break;
                case "-help":
                case "--help":
                    printUsage();
                    System.exit(0);
                    break;
                default:
                    // Try to parse as seed if it's a number
                    try {
                        seed = Long.parseLong(args[i]);
                    } catch (NumberFormatException e) {
                        System.err.println("Warning: Unknown argument '" + args[i] + "', ignoring");
                    }
                    break;
            }
        }

        return new ParsedConfig(seed, width, height, loadGame, saveFile);
    }

    /**
     * Initializes all major game systems in the correct order.
     *
     * @param config Game configuration settings
     */
    private static void initializeGameSystems(ParsedConfig config) {
        System.out.println("Initializing Pac-Man game systems...");
        System.out.println("Configuration: " + config.width + "x" + config.height +
                ", Seed: " + config.seed);

        // TODO: Initialize save system first when SaveData class is available
        // saveData = new SaveData();
        // if (config.loadGame) {
        //     loadGameState(config.saveFile);
        // }

        // Initialize game engine with world dimensions (from existing constructor)
        gameEngine = new GameEngine(config.width, config.height);
        gameEngine.initialize();

        // TODO: Initialize and set managers when setter methods are available in GameEngine
        // scoreManager = new ScoreManager();
        // levelManager = new LevelManager(saveData, scoreManager); 
        // worldManager = new WorldManager(config.seed, config.width, config.height);
        // gameEngine.setWorldManager(worldManager);
        // gameEngine.setLevelManager(levelManager);
        // gameEngine.setScoreManager(scoreManager);
        // gameEngine.setSaveData(saveData);

        System.out.println("Game engine created - system initialization pending");
    }

    /**
     * Starts the main game loop.
     * Handles the initial game state and begins execution.
     */
    private static void startGame() {
        System.out.println("Starting Pac-Man game...");

        // TODO: Set initial game state when GameEngine.setState() method is available
        // if (saveData != null && saveData.hasValidSave()) {
        //     System.out.println("Resuming from saved game");
        //     gameEngine.setState(GameState.HUB_WORLD); // Resume in hub world
        // } else {
        //     System.out.println("Starting new game");
        //     gameEngine.setState(GameState.START_MENU); // New game starts at menu
        // }

        gameEngine.setState(GameState.START_MENU);

        // Set default initial state
        System.out.println("Starting new game with default state");

        // TODO: Start the main game loop when GameEngine.run() method is implemented
        gameEngine.gameLoop();

        // Temporary: Just report that game engine is ready
        System.out.println("Game engine initialized and ready to run");
        System.out.println("TODO: Implement GameEngine.run() method to start the main game loop");
    }

    /**
     * Loads game state from save file.
     * TODO: Implement when SaveData class is available
     *
     * @param saveFile Path to save file (null for default)
     */
    private static void loadGameState(String saveFile) {
        // TODO: Implement save loading when SaveData class is available
        // try {
        //     if (saveFile != null) {
        //         saveData.loadFromPath(saveFile);
        //     } else {
        //         saveData.loadFromDefaultLocation();
        //     }
        //     System.out.println("Game state loaded successfully");
        // } catch (Exception e) {
        //     System.err.println("Warning: Could not load save file: " + e.getMessage());
        //     System.out.println("Starting new game instead");
        // }

        System.out.println("Save loading not yet implemented - starting fresh game");
    }

    /**
     * Prints usage information for command line arguments.
     */
    private static void printUsage() {
        System.out.println("Pac-Man Game - Usage:");
        System.out.println("java Main [options]");
        System.out.println();
        System.out.println("Options:");
        System.out.println("  -seed <number>     Set world generation seed");
        System.out.println("  -width <number>    Set world width (40-200)");
        System.out.println("  -height <number>   Set world height (30-100)");
        System.out.println("  -load [file]       Load saved game (optional file path)");
        System.out.println("  -help              Show this help message");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  java Main                           # Start with defaults");
        System.out.println("  java Main -seed 12345               # Use specific seed");
        System.out.println("  java Main -width 100 -height 60     # Custom world size");
        System.out.println("  java Main -load                     # Load default save");
        System.out.println("  java Main -load my_save.dat         # Load specific save");
    }

    /**
     * Graceful shutdown handler.
     * Ensures proper cleanup of resources and save data.
     */
    public static void shutdown() {
        System.out.println("Shutting down Pac-Man game...");

        try {
            // TODO: Save current game state when SaveData class is available
            // if (gameEngine != null && saveData != null) {
            //     saveData.captureGameState(gameEngine);
            //     saveData.saveToDefaultLocation();
            //     System.out.println("Game state saved");
            // }

            // Clean up resources using existing GameEngine.shutdown() method
            if (gameEngine != null) {
                gameEngine.shutdown();
                System.out.println("Game engine shutdown completed");
            }

            System.out.println("TODO: Implement save system for graceful shutdown");

        } catch (Exception e) {
            System.err.println("Error during shutdown: " + e.getMessage());
        }

        System.out.println("Shutdown complete");
        System.exit(0);
    }

    /**
     * Gets the current game engine instance.
     * Used for integration testing and debugging.
     *
     * @return Current GameEngine instance
     */
    public static GameEngine getGameEngine() {
        return gameEngine;
    }

    /**
     * Simple configuration holder class for parsed command line arguments.
     */
    private static class ParsedConfig {
        final long seed;
        final int width;
        final int height;
        final boolean loadGame;
        final String saveFile;

        ParsedConfig(long seed, int width, int height, boolean loadGame, String saveFile) {
            this.seed = seed;
            this.width = width;
            this.height = height;
            this.loadGame = loadGame;
            this.saveFile = saveFile;
        }
    }
}
