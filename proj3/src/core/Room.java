package core;

public class Room {
    int x, y, width, height;

    public Room (int x, int y, int w, int h) {
        this.x = x;
        this.y = y;
        this.width = w;
        this.height = h;
    }

    public boolean intersects(Room other) {
        // Allow rooms to be very close together (minimal gap)
        return (this.x < other.x + other.width &&
                this.x + this.width > other.x &&
                this.y < other.y + other.height &&
                this.y + this.height > other.y);
    }


    public int centerX() { return x + width / 2; }
    public int centerY() { return y + height / 2; }

    public Pos center() {
        return new Pos(x + width / 2, y + height / 2);
    }
}
