package config;

/**
 * Centralized configuration constants for the Pac-Man game.
 * Contains all configurable game parameters and settings.
 * <p>
 * This class provides:
 * - Movement speeds for different entities
 * - Scoring values and multipliers
 * - Skill durations and cooldowns
 * - World generation parameters
 * - Performance and rendering settings
 */
public final class GameConfig {

    // Prevent instantiation
    private GameConfig() {
    }

    // ========== WORLD SETTINGS ==========

    /**
     * Default world width in tiles
     */
    public static final int DEFAULT_WORLD_WIDTH = 50;

    /**
     * Default world height in tiles
     */
    public static final int DEFAULT_WORLD_HEIGHT = 50;

    /**
     * Size of each world chunk in tiles
     */
    public static final int CHUNK_SIZE = 50;

    /**
     * Tile size in pixels for rendering
     */
    public static final int TILE_SIZE = 16;

    /**
     * Maximum number of chunks to keep loaded
     */
    public static final int MAX_LOADED_CHUNKS = 25;

    /**
     * Maximum number of chunks to keep cached
     */
    public static final int MAX_CACHED_CHUNKS = 50;

    // ========== MOVEMENT SPEEDS ==========

    /**
     * Player (avatar) movement speed in tiles per second
     */
    public static final double PLAYER_SPEED = 2.0;

    /**
     * Blue ghost movement speed ratio relative to player speed
     */
    private static final double BLUE_GHOST_SPEED_RATIO = 1.0;

    /**
     * Blue ghost movement speed in tiles per second (same as avatar)
     */
    public static final double BLUE_GHOST_SPEED = BLUE_GHOST_SPEED_RATIO * PLAYER_SPEED;

    /**
     * Blue ghost movement speed ratio relative to player speed
     */
    private static final double PINK_GHOST_SPEED_RATIO = 1.5;
    
    /**
     * Pink ghost movement speed in tiles per second (1.5x avatar speed)
     */
    public static final double PINK_GHOST_SPEED = PINK_GHOST_SPEED_RATIO * PLAYER_SPEED;

    /**
     * Blue ghost movement speed ratio relative to player speed
     */
    private static final double ORAGNE_GHOST_SPEED_RATIO = 1.0;

    /**
     * Orange ghost movement speed in tiles per second (same as avatar)
     */
    public static final double ORANGE_GHOST_SPEED = ORAGNE_GHOST_SPEED_RATIO * PLAYER_SPEED;

    /**
     * Blue ghost movement speed ratio relative to player speed
     */
    private static final double RED_GHOST_SPEED_RATIO = 1.2;

    /**
     * Red ghost movement speed in tiles per second (1.2x avatar speed)
     */
    public static final double RED_GHOST_SPEED = RED_GHOST_SPEED_RATIO * PLAYER_SPEED;

    /**
     * Ghost speed when frightened (50% of normal speed)
     */
    public static final double FRIGHTENED_GHOST_SPEED_MULTIPLIER = 0.5;

    /**
     * Ghost speed when returning to base after being eaten (2x normal speed)
     */
    public static final double EATEN_GHOST_SPEED_MULTIPLIER = 2.0;

    // ========== SCORING SYSTEM ==========

    /**
     * Points awarded for collecting a regular pellet
     */
    public static final int PELLET_SCORE = 1;

    /**
     * Points awarded for collecting a power pellet
     */
    public static final int POWER_PELLET_SCORE = 10;

    /**
     * Base points awarded for eating a ghost
     */
    public static final int GHOST_BASE_SCORE = 200;

    /**
     * Score multiplier for consecutive ghost eating (doubles each time)
     */
    public static final int GHOST_SCORE_MULTIPLIER = 2;

    /**
     * Bonus points for various achievements
     */
    public static final int BONUS_SCORE = 100;

    /**
     * Score display format pattern
     */
    public static final String SCORE_FORMAT_PATTERN = "#,###";

    // ========== SKILL SYSTEM ==========

    /**
     * Power pellet effect duration in seconds
     */
    public static final double POWER_PELLET_DURATION = 3.0;

    /**
     * Warning time before power pellet expires (start blinking)
     */
    public static final double POWER_PELLET_WARNING_TIME = 1.0;

    /**
     * Cooldown time between skill activations
     * 
     * TODO: I doubt if this actually needed. Normally player won't be able to collect two skills in such a short time.
     */
    public static final double SKILL_COOLDOWN = 0.5;

    /**
     * Maximum number of active skills at once
     */
    public static final int MAX_ACTIVE_SKILLS = 3;

    // ========== AI BEHAVIOR SETTINGS ==========

    /**
     * Blue ghost patrol radius in tiles
     */
    public static final double BLUE_GHOST_PATROL_RADIUS = 5.0;

    /**
     * Pink ghost line-of-sight range in tiles
     */
    public static final double PINK_GHOST_SIGHT_RANGE = 12.0;

    /**
     * Pink ghost maximum charge duration in seconds
     */
    public static final double PINK_GHOST_CHARGE_DURATION = 5.0;

    /**
     * Orange ghost tracking range in tiles
     */
    public static final double ORANGE_GHOST_TRACKING_RANGE = 8.0;

    /**
     * Orange ghost lose tracking range in tiles (hysteresis)
     */
    public static final double ORANGE_GHOST_LOSE_TRACKING_RANGE = 10.0;

    /**
     * Red ghost pathfinding recalculation interval in seconds
     */
    public static final double RED_GHOST_PATH_RECALC_INTERVAL = 1.0;

    /**
     * Red ghost maximum path length in nodes
     */
    public static final int RED_GHOST_MAX_PATH_LENGTH = 50;

    // ========== GAME MECHANICS ==========

    /**
     * Starting number of lives for player
     */
    public static final int STARTING_LIVES = 3;

    /**
     * Maximum number of lives player can have
     */
    public static final int MAX_LIVES = 5;

    /**
     * Game frame rate target in FPS
     */
    public static final int TARGET_FPS = 60;

    /**
     * Game update rate in milliseconds
     */
    public static final int UPDATE_RATE_MS = 1000 / TARGET_FPS; // ~60 FPS 

    /**
     * Input polling rate in milliseconds
     */
    public static final int INPUT_POLL_RATE_MS = 10;

    // ========== AUDIO SETTINGS ==========

    /**
     * Default master volume (0.0 to 1.0)
     */
    public static final double DEFAULT_MASTER_VOLUME = 0.8;

    /**
     * Default music volume (0.0 to 1.0)
     */
    public static final double DEFAULT_MUSIC_VOLUME = 0.6;

    /**
     * Default sound effects volume (0.0 to 1.0)
     */
    public static final double DEFAULT_SFX_VOLUME = 0.8;

    /**
     * Audio buffer size for streaming
     */
    public static final int AUDIO_BUFFER_SIZE = 4096;

    // ========== RENDERING SETTINGS ==========

    /**
     * Enable smooth rendering by default
     */
    public static final boolean DEFAULT_SMOOTH_RENDERING = true;

    /**
     * Enable entity interpolation by default
     */
    public static final boolean DEFAULT_ENTITY_INTERPOLATION = true;

    /**
     * Enable visual effects by default
     */
    public static final boolean DEFAULT_VISUAL_EFFECTS = true;

    /**
     * Camera follow speed for smooth scrolling
     */
    public static final double CAMERA_FOLLOW_SPEED = 5.0;

    /**
     * Camera follow deadzone radius
     */
    public static final double CAMERA_DEADZONE = 2.0;

    /**
     * Default zoom level
     */
    public static final double DEFAULT_ZOOM_LEVEL = 1.0;

    /**
     * Minimum zoom level
     */
    public static final double MIN_ZOOM_LEVEL = 0.5;

    /**
     * Maximum zoom level
     */
    public static final double MAX_ZOOM_LEVEL = 3.0;

    // ========== PERFORMANCE SETTINGS ==========

    /**
     * Maximum number of entities to update per frame
     */
    public static final int MAX_ENTITIES_PER_FRAME = 100;

    /**
     * Maximum number of A* nodes to process per frame
     */
    public static final int MAX_ASTAR_NODES_PER_FRAME = 50;

    /**
     * Asset cache size limit
     */
    public static final int ASSET_CACHE_SIZE = 1000;

    /**
     * Enable performance monitoring
     */
    public static final boolean ENABLE_PERFORMANCE_MONITORING = false;

    // ========== UI SETTINGS ==========

    /**
     * HUD height in pixels
     */
    public static final double HUD_HEIGHT = 60;

    /**
     * HUD margin from screen edges
     */
    public static final double HUD_MARGIN = 10;

    /**
     * Spacing between HUD elements
     */
    public static final double HUD_ELEMENT_SPACING = 20;

    /**
     * Menu animation duration in seconds
     */
    public static final double MENU_ANIMATION_DURATION = 0.3;

    /**
     * Menu selection highlight intensity
     */
    public static final double MENU_HIGHLIGHT_INTENSITY = 1.5;

    // ========== SAVE SYSTEM ==========

    /**
     * Save file name for game state
     */
    public static final String SAVE_FILE_NAME = "pacman_save.json";

    /**
     * High scores file name
     */
    public static final String HIGH_SCORES_FILE_NAME = "highscores.dat";

    /**
     * Settings file name
     */
    public static final String SETTINGS_FILE_NAME = "settings.json";

    /**
     * Maximum number of high scores to keep
     */
    public static final int MAX_HIGH_SCORES = 10;

    /**
     * Auto-save interval in seconds
     */
    public static final double AUTO_SAVE_INTERVAL = 30.0;

    // ========== DEVELOPMENT SETTINGS ==========

    /**
     * Enable debug mode
     */
    public static final boolean DEBUG_MODE = false;

    /**
     * Show debug information on screen
     */
    public static final boolean SHOW_DEBUG_INFO = false;

    /**
     * Enable collision debug visualization
     */
    public static final boolean DEBUG_COLLISIONS = false;

    /**
     * Enable AI debug visualization
     */
    public static final boolean DEBUG_AI = false;

    /**
     * Enable performance debug logging
     */
    public static final boolean DEBUG_PERFORMANCE = false;

    // ========== VALIDATION METHODS ==========

    /**
     * Validates a speed value is within reasonable bounds.
     *
     * @param speed Speed value to validate
     * @return Clamped speed value
     */
    public static double validateSpeed(double speed) {
        return Math.max(0.1, Math.min(10.0, speed));
    }

    /**
     * Validates a volume value is within audio bounds.
     *
     * @param volume Volume value to validate
     * @return Clamped volume value (0.0 to 1.0)
     */
    public static double validateVolume(double volume) {
        return Math.max(0.0, Math.min(1.0, volume));
    }

    /**
     * Validates a zoom level is within rendering bounds.
     *
     * @param zoom Zoom level to validate
     * @return Clamped zoom level
     */
    public static double validateZoom(double zoom) {
        return Math.max(MIN_ZOOM_LEVEL, Math.min(MAX_ZOOM_LEVEL, zoom));
    }

    /**
     * Validates a score value is non-negative.
     *
     * @param score Score value to validate
     * @return Validated score value
     */
    public static int validateScore(int score) {
        return Math.max(0, score);
    }

    /**
     * Validates a duration value is positive.
     *
     * @param duration Duration to validate
     * @return Validated duration value
     */
    public static double validateDuration(double duration) {
        return Math.max(0.1, duration);
    }

    /**
     * Gets the frame time in seconds for target FPS.
     *
     * @return Frame time in seconds
     */
    public static double getFrameTime() {
        return 1.0 / TARGET_FPS;
    }

    /**
     * Gets the update time in seconds for target update rate.
     *
     * @return Update time in seconds
     */
    public static double getUpdateTime() {
        return UPDATE_RATE_MS / 1000.0;
    }

    /**
     * Calculates ghost speed based on state and multipliers.
     *
     * @param baseSpeed    Base ghost speed
     * @param isFrightened Whether ghost is frightened
     * @param isEaten      Whether ghost is eaten (returning to base)
     * @return Calculated ghost speed
     */
    public static double calculateGhostSpeed(double baseSpeed, boolean isFrightened, boolean isEaten) {
        if (isEaten) {
            return baseSpeed * EATEN_GHOST_SPEED_MULTIPLIER;
        } else if (isFrightened) {
            return baseSpeed * FRIGHTENED_GHOST_SPEED_MULTIPLIER;
        } else {
            return baseSpeed;
        }
    }

    /**
     * Calculates score for eating ghosts with multiplier.
     *
     * @param ghostCount Number of ghosts eaten consecutively
     * @return Score for eating the ghost
     */
    public static int calculateGhostScore(int ghostCount) {
        return GHOST_BASE_SCORE * (int) Math.pow(GHOST_SCORE_MULTIPLIER, ghostCount - 1);
    }
}