package config;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

/**
 * Asset file path constants for easy asset management.
 * Contains all file paths for sprites, audio, themes, and other game assets.
 * <p>
 * This class provides:
 * - Centralized asset path management
 * - Easy asset path modification
 * - Asset organization by category
 * - Platform-independent path handling
 */
public final class AssetPaths {

    // Prevent instantiation
    private AssetPaths() {
    }

    // ========== ENUMS for type-safe asset access ==========

    public enum GhostColor {
        RED("red"), PIN<PERSON>("pink"), BLUE("blue"), ORANGE("orange");

        private final String fileName;

        GhostColor(String fileName) {
            this.fileName = fileName;
        }

        public String getFileName() {
            return fileName;
        }
    }

    public enum Direction {
        UP, DOWN, LEFT, RIGHT, NORMAL
    }

    public enum WallType {
        BASIC("basic"),
        CORNER_TL("corner_tl"),
        CORNER_TR("corner_tr"),
        CORNER_BL("corner_bl"),
        CORNER_BR("corner_br"),
        HORIZONTAL("horizontal"),
        VERTICAL("vertical");

        private final String fileName;

        WallType(String fileName) {
            this.fileName = fileName;
        }

        public String getFileName() {
            return fileName;
        }
    }

    // ========== BASE DIRECTORIES ==========

    /**
     * Base directory for all assets
     */
    public static final String ASSETS_BASE = "assets/";

    /**
     * Sprites directory
     */
    public static final String SPRITES_DIR = ASSETS_BASE + "sprites/";

    /**
     * Audio directory
     */
    public static final String AUDIO_DIR = ASSETS_BASE + "audio/";

    /**
     * Themes directory
     */
    public static final String THEMES_DIR = ASSETS_BASE + "themes/";

    /**
     * UI elements directory
     */
    public static final String UI_DIR = ASSETS_BASE + "ui/";

    /**
     * Data files directory
     */
    public static final String DATA_DIR = ASSETS_BASE + "data/";

    // ========== PLAYER SPRITES ==========

    /**
     * Player sprite animation frames
     */
    public static final String PLAYER_SPRITE_0 = SPRITES_DIR + "avatar_0.png";
    public static final String PLAYER_SPRITE_1 = SPRITES_DIR + "avatar_1.png";
    public static final String PLAYER_SPRITE_2 = SPRITES_DIR + "avatar_2.png";
    public static final String PLAYER_SPRITE_3 = SPRITES_DIR + "avatar_3.png";

    /**
     * Player directional sprites
     */
    public static final String PLAYER_UP = SPRITES_DIR + "player_up.png";
    public static final String PLAYER_DOWN = SPRITES_DIR + "player_down.png";
    public static final String PLAYER_LEFT = SPRITES_DIR + "player_left.png";
    public static final String PLAYER_RIGHT = SPRITES_DIR + "player_right.png";

    /**
     * Player powered-up sprites
     */
    public static final String PLAYER_POWERED_UP = SPRITES_DIR + "player_powered.png";

    // ========== GHOST SPRITES ==========

    /**
     * Ghost sprites directory
     */
    public static final String GHOSTS_SPRITES_DIR = SPRITES_DIR + "ghosts/";

    /**
     * Frightened ghost sprites
     */
    public static final String GHOST_FRIGHTENED = GHOSTS_SPRITES_DIR + "ghost_frightened.png";
    public static final String GHOST_FRIGHTENED_FLASH = GHOSTS_SPRITES_DIR + "ghost_frightened_flash.png";

    /**
     * Eaten ghost sprites (eyes only)
     */
    public static final String GHOST_EYES_UP = GHOSTS_SPRITES_DIR + "ghost_eyes_up.png";
    public static final String GHOST_EYES_DOWN = GHOSTS_SPRITES_DIR + "ghost_eyes_down.png";
    public static final String GHOST_EYES_LEFT = GHOSTS_SPRITES_DIR + "ghost_eyes_left.png";
    public static final String GHOST_EYES_RIGHT = GHOSTS_SPRITES_DIR + "ghost_eyes_right.png";

    // ========== WORLD SPRITES ==========

    /**
     * Wall sprites directory
     */
    public static final String WALLS_SPRITES_DIR = SPRITES_DIR + "walls/";

    /**
     * Floor and pellet sprites
     */
    public static final String FLOOR = SPRITES_DIR + "floor.png";
    public static final String PELLET = SPRITES_DIR + "pellet.png";
    public static final String POWER_PELLET = SPRITES_DIR + "power_pellet.png";
    public static final String POWER_PELLET_GLOW = SPRITES_DIR + "power_pellet_glow.png";

    // ========== UI SPRITES ==========

    /**
     * UI elements directory
     */
    public static final String UI_SPRITES_DIR = UI_DIR + "sprites/";

    /**
     * Menu sprites
     */
    public static final String MENU_BACKGROUND = UI_SPRITES_DIR + "menu_background.png";
    public static final String MENU_TITLE = UI_SPRITES_DIR + "menu_title.png";
    public static final String MENU_CURSOR = UI_SPRITES_DIR + "menu_cursor.png";

    /**
     * HUD sprites
     */
    public static final String HUD_HEART = UI_SPRITES_DIR + "heart.png";
    public static final String HUD_HEART_EMPTY = UI_SPRITES_DIR + "heart_empty.png";
    public static final String HUD_SCORE_FRAME = UI_SPRITES_DIR + "score_frame.png";
    public static final String HUD_SKILL_BAR = UI_SPRITES_DIR + "skill_bar.png";
    public static final String HUD_SKILL_ICON = UI_SPRITES_DIR + "skill_icon.png";

    /**
     * Progress bars and indicators
     */
    public static final String PROGRESS_BAR_FRAME = UI_SPRITES_DIR + "progress_bar_frame.png";
    public static final String PROGRESS_BAR_FILL = UI_SPRITES_DIR + "progress_bar_fill.png";
    public static final String CIRCULAR_PROGRESS = UI_SPRITES_DIR + "circular_progress.png";

    // ========== AUDIO FILES ==========

    /**
     * Music directory
     */
    public static final String MUSIC_DIR = AUDIO_DIR + "music/";

    /**
     * Background music
     */
    public static final String MENU_MUSIC = MUSIC_DIR + "menu_theme.mp3";
    public static final String GAME_MUSIC = MUSIC_DIR + "game_theme.mp3";
    public static final String POWER_UP_MUSIC = MUSIC_DIR + "power_up_theme.mp3";

    /**
     * Sound effects directory
     */
    public static final String SFX_DIR = AUDIO_DIR + "sfx/";

    /**
     * Game sound effects
     */
    public static final String SFX_PELLET_COLLECT = SFX_DIR + "pellet_collect.wav";
    public static final String SFX_POWER_PELLET = SFX_DIR + "power_pellet.wav";
    public static final String SFX_GHOST_EATEN = SFX_DIR + "ghost_eaten.wav";
    public static final String SFX_PLAYER_DEATH = SFX_DIR + "player_death.wav";
    public static final String SFX_GAME_OVER = SFX_DIR + "game_over.wav";
    public static final String SFX_LEVEL_COMPLETE = SFX_DIR + "level_complete.wav";

    /**
     * UI sound effects
     */
    public static final String SFX_MENU_SELECT = SFX_DIR + "menu_select.wav";
    public static final String SFX_MENU_NAVIGATE = SFX_DIR + "menu_navigate.wav";
    public static final String SFX_MENU_BACK = SFX_DIR + "menu_back.wav";
    public static final String SFX_BUTTON_CLICK = SFX_DIR + "button_click.wav";

    // ========== THEME FILES ==========

    /**
     * Theme configuration files
     */
    public static final String TOKYO_NIGHT_THEME = THEMES_DIR + "tokyo_night.json";
    public static final String MONOKAI_THEME = THEMES_DIR + "monokai.json";
    public static final String GROOVEBOX_THEME = THEMES_DIR + "groovebox.json";
    public static final String CLASSIC_THEME = THEMES_DIR + "classic.json";
    public static final String CUSTOM_THEME = THEMES_DIR + "custom.json";

    // ========== DATA FILES ==========

    /**
     * Game data files
     */
    public static final String DEFAULT_WORLD_DATA = DATA_DIR + "default_world.json";
    public static final String ENTITY_DEFINITIONS = DATA_DIR + "entity_definitions.json";
    public static final String SKILL_DEFINITIONS = DATA_DIR + "skill_definitions.json";

    /**
     * Localization files
     */
    public static final String LOCALIZATION_DIR = DATA_DIR + "localization/";
    public static final String STRINGS_EN = LOCALIZATION_DIR + "strings_en.json";

    // ========== FALLBACK SPRITES ==========

    /**
     * Fallback sprites when assets fail to load
     */
    public static final String FALLBACK_SPRITE = SPRITES_DIR + "fallback.png";
    public static final String MISSING_TEXTURE = SPRITES_DIR + "missing_texture.png";

    // ========== UTILITY METHODS ==========

    /**
     * Gets the full path for a sprite file.
     *
     * @param spriteName Name of the sprite file
     * @return Full path to sprite
     */
    public static String getSpritePath(String spriteName) {
        return SPRITES_DIR + spriteName;
    }

    /**
     * Gets the full path for an audio file.
     *
     * @param audioName Name of the audio file
     * @return Full path to audio file
     */
    public static String getAudioPath(String audioName) {
        return AUDIO_DIR + audioName;
    }

    /**
     * Gets the full path for a theme file.
     *
     * @param themeName Name of the theme file
     * @return Full path to theme file
     */
    public static String getThemePath(String themeName) {
        return THEMES_DIR + themeName;
    }

    /**
     * Gets the full path for a UI element.
     *
     * @param uiElementName Name of the UI element
     * @return Full path to UI element
     */
    public static String getUIPath(String uiElementName) {
        return UI_SPRITES_DIR + uiElementName;
    }

    /**
     * Gets player sprite path for a specific animation frame.
     *
     * @param frameIndex Animation frame index (0-3)
     * @return Path to player sprite frame
     */
    public static String getPlayerSprite(int frameIndex) {
        switch (frameIndex % 4) {
            case 0:
                return PLAYER_SPRITE_0;
            case 1:
                return PLAYER_SPRITE_1;
            case 2:
                return PLAYER_SPRITE_2;
            case 3:
                return PLAYER_SPRITE_3;
            default:
                return PLAYER_SPRITE_0;
        }
    }

    /**
     * Gets ghost sprite path for a specific color and direction.
     *
     * @param color     Ghost color
     * @param direction Movement direction
     * @return Path to ghost sprite
     */
    public static String getGhostSprite(GhostColor color, Direction direction) {
        String colorStr = color.getFileName();
        String directionStr = direction.toString().toLowerCase();
        String basePath = GHOSTS_SPRITES_DIR + colorStr + "_ghost";
        if (direction == Direction.NORMAL) {
            return basePath + ".png";
        } else {
            return basePath + "_" + directionStr + ".png";
        }
    }

    /**
     * Gets wall sprite path for a specific wall type.
     *
     * @param wallType Type of wall
     * @return Path to wall sprite
     */
    public static String getWallSprite(WallType wallType) {
        return WALLS_SPRITES_DIR + "wall_" + wallType.getFileName() + ".png";
    }

    /**
     * Checks if an asset path exists and is valid.
     *
     * @param assetPath Path to check
     * @return true if path appears valid
     */
    public static boolean isValidAssetPath(String assetPath) {
        return assetPath != null &&
                assetPath.startsWith(ASSETS_BASE) &&
                !assetPath.equals(ASSETS_BASE);
    }

    /**
     * Gets the file extension from an asset path.
     *
     * @param assetPath Asset path
     * @return File extension (without dot), or empty string if none
     */
    public static String getFileExtension(String assetPath) {
        if (assetPath == null) return "";
        int lastDot = assetPath.lastIndexOf('.');
        if (lastDot == -1 || lastDot == assetPath.length() - 1) return "";
        return assetPath.substring(lastDot + 1).toLowerCase();
    }

    /**
     * Gets the filename without extension from an asset path.
     *
     * @param assetPath Asset path
     * @return Filename without extension
     */
    public static String getFileNameWithoutExtension(String assetPath) {
        if (assetPath == null) return "";
        String fileName = assetPath.substring(assetPath.lastIndexOf('/') + 1);
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot == -1) return fileName;
        return fileName.substring(0, lastDot);
    }

    /**
     * Go through all the static fields and check if every asset file is present or not.
     *
     * @param silence print out a summary text when called if set to true
     * @return true if all assets are present, false it some assets is missing
     */
    public static boolean checkHealth(boolean silence) {
        Field[] fields = AssetPaths.class.getDeclaredFields();
        List<String> missingAssets = new ArrayList<>();
        List<String> foundAssets = new ArrayList<>();

        for (Field field : fields) {
            if (Modifier.isStatic(field.getModifiers()) && field.getType().equals(String.class)) {
                try {
                    String assetPath = (String) field.get(null);
                    if (assetPath.endsWith("/")) {
                        continue; // Skip directories
                    }
                    File file = new File(assetPath);
                    if (file.exists()) {
                        foundAssets.add(assetPath);
                    } else {
                        missingAssets.add(assetPath);
                    }
                } catch (IllegalAccessException e) {
                    // This should not happen for public static fields
                    if (!silence) {
                        System.err.println("Error accessing field: " + field.getName());
                    }
                }
            }
        }

        if (!silence) {
            int totalAssets = foundAssets.size() + missingAssets.size();
            System.out.println("--- Asset Health Check ---");
            System.out.println("Total assets checked: " + totalAssets);
            System.out.println("Found: " + foundAssets.size());
            System.out.println("Missing: " + missingAssets.size());

            if (!missingAssets.isEmpty()) {
                System.err.println("\nMissing asset details:");
                for (String missing : missingAssets) {
                    System.err.println(" - " + missing);
                }
            } else {
                System.out.println("\nAll assets are accounted for. Great job!");
            }
            System.out.println("--- End of Report ---");
        }

        return missingAssets.isEmpty();
    }

    public static void main(String[] args) {
        checkHealth(false);
    }
}
