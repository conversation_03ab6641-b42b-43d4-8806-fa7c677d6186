package managers;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Score tracking manager with pellet values and formatting.
 * Manages player score, high scores, and score-related events.
 * <p>
 * This manager provides:
 * - Current score tracking with configurable point values
 * - High score persistence and management
 * - Score formatting with comma separation
 * - Score event handling and multipliers
 * - Achievement and milestone tracking
 */
public class ScoreManager {
    // Current game score
    private int currentScore;
    private int sessionScore; // Score gained this session

    // Score values for different items
    private Map<String, Integer> scoreValues;

    // Score multipliers and bonuses
    private double scoreMultiplier;
    private int comboCount; // For combo scoring
    private long lastScoreTime; // For combo timing
    private static final long COMBO_TIMEOUT = 2000; // 2 seconds for combo

    // High scores
    private List<HighScore> highScores;
    private int maxHighScores;
    private String highScoreFile;

    // Score formatting
    private DecimalFormat scoreFormatter;

    // Score events and listeners
    private List<ScoreListener> scoreListeners;

    /**
     * Interface for score event listeners.
     */
    public interface ScoreListener {
        void onScoreChanged(int newScore, int scoreGained);

        void onHighScore(int score, int rank);

        void onComboScore(int comboCount, int bonusPoints);
    }

    /**
     * High score entry class.
     */
    public static class HighScore {
        public final String playerName;
        public final int score;
        public final long timestamp;

        public HighScore(String playerName, int score, long timestamp) {
            this.playerName = playerName;
            this.score = score;
            this.timestamp = timestamp;
        }

        @Override
        public String toString() {
            return String.format("%s: %,d", playerName, score);
        }
    }

    /**
     * Creates a ScoreManager with default settings.
     */
    public ScoreManager() {
        // Set currentScore = 0, sessionScore = 0
        this.currentScore = 0;
        this.sessionScore = 0;

        // Initialize scoreValues map with default values
        this.scoreValues = new HashMap<>();
        scoreValues.put("pellet", 1);           // 1 point (from specification)
        scoreValues.put("power_pellet", 10);    // 10 points
        scoreValues.put("ghost", 200);          // 200 points
        scoreValues.put("bonus", 100);          // 100 points

        // Set scoreMultiplier = 1.0
        this.scoreMultiplier = 1.0;

        // Set comboCount = 0, lastScoreTime = 0
        this.comboCount = 0;
        this.lastScoreTime = 0;

        // Initialize highScores list and set maxHighScores = 10
        this.highScores = new ArrayList<>();
        this.maxHighScores = 10;

        // Set highScoreFile = "highscores.dat"
        this.highScoreFile = "highscores.dat";

        // Create scoreFormatter with comma separators
        this.scoreFormatter = new DecimalFormat("#,###");

        // Initialize scoreListeners list
        this.scoreListeners = new ArrayList<>();
    }

    /**
     * Creates a ScoreManager with custom high score settings.
     *
     * @param maxHighScores Maximum number of high scores to keep
     * @param highScoreFile File path for high score persistence
     */
    public ScoreManager(int maxHighScores, String highScoreFile) {
        // Call default constructor for base initialization
        this();

        // Validate parameters (positive maxHighScores, non-null file path)
        if (maxHighScores <= 0) {
            throw new IllegalArgumentException("maxHighScores must be positive");
        }
        if (highScoreFile == null) {
            throw new IllegalArgumentException("highScoreFile cannot be null");
        }

        // Override maxHighScores and highScoreFile with provided values
        this.maxHighScores = maxHighScores;
        this.highScoreFile = highScoreFile;
    }

    /**
     * Initializes the score manager and loads high scores.
     */
    public void initialize() {
        // Load high scores from file with loadHighScores()
        loadHighScores();

        // Set up score formatting (already done in constructor)
        // Initialize score tracking variables (already done in constructor)
        System.out.println("ScoreManager initialized with " + highScores.size() + " high scores loaded");
    }

    /**
     * Adds points to the current score.
     *
     * @param points Points to add
     * @param source Source of the points (for event tracking)
     */
    public void addScore(int points, String source) {
        if (points <= 0) return;

        long currentTime = System.currentTimeMillis();

        // Update combo if within timeout
        updateCombo(currentTime);

        // Calculate actual points with multiplier and combo
        int basePoints = (int) (points * scoreMultiplier);
        int comboBonus = calculateComboBonus(basePoints);
        int totalPoints = basePoints + comboBonus;

        // Add to currentScore and sessionScore
        this.currentScore += totalPoints;
        this.sessionScore += totalPoints;

        // Update lastScoreTime
        this.lastScoreTime = currentTime;

        // Check for high score achievement and notify listeners
        notifyScoreListeners(totalPoints);

        // Notify combo listeners if applicable
        if (comboCount > 1) {
            notifyComboListeners(comboCount, comboBonus);
        }
    }

    /**
     * Adds score for a specific item type.
     *
     * @param itemType Type of item collected (pellet, power_pellet, ghost, etc.)
     */
    public void addScore(String itemType) {
        if (itemType == null) return;

        // Get point value from scoreValues map
        Integer points = scoreValues.get(itemType);

        if (points != null) {
            // Call addScore(points, itemType) for actual scoring
            addScore(points, itemType);
        } else {
            // If itemType not found, use default value or log warning
            System.err.println("Warning: Unknown item type '" + itemType + "', using default value of 1");
            addScore(1, itemType);
        }
    }

    /**
     * Sets the score multiplier for bonus scoring periods.
     *
     * @param multiplier Score multiplier (1.0 = normal, 2.0 = double points, etc.)
     */
    public void setScoreMultiplier(double multiplier) {
        // Validate multiplier >= 0
        if (multiplier < 0) {
            throw new IllegalArgumentException("Score multiplier cannot be negative");
        }

        // Set this.scoreMultiplier = multiplier
        this.scoreMultiplier = multiplier;

        // Notify listeners of multiplier change if applicable
        System.out.println("Score multiplier set to: " + multiplier);
    }

    /**
     * Resets the score multiplier to normal (1.0).
     */
    public void resetScoreMultiplier() {
        setScoreMultiplier(1.0);
    }

    /**
     * Updates combo scoring based on rapid successive scoring.
     *
     * @param currentTime Current system time in milliseconds
     */
    private void updateCombo(long currentTime) {
        // Check if current scoring is within COMBO_TIMEOUT of lastScoreTime
        if (lastScoreTime > 0 && (currentTime - lastScoreTime) <= COMBO_TIMEOUT) {
            // If within timeout: increment comboCount
            comboCount++;
        } else {
            // If timeout exceeded: reset comboCount to 1
            comboCount = 1;
        }
    }

    /**
     * Calculates bonus points for combo scoring.
     *
     * @param basePoints Base points before combo bonus
     * @return Bonus points from combo multiplier
     */
    private int calculateComboBonus(int basePoints) {
        // If comboCount <= 1, return 0 (no bonus)
        if (comboCount <= 1) {
            return 0;
        }

        // Calculate bonus based on combo count
        double bonusMultiplier;
        if (comboCount == 2) {
            bonusMultiplier = 0.10; // 10% bonus
        } else if (comboCount == 3) {
            bonusMultiplier = 0.25; // 25% bonus
        } else if (comboCount == 4) {
            bonusMultiplier = 0.50; // 50% bonus
        } else {
            bonusMultiplier = 1.00; // 100% bonus for 5+ combo
        }

        // Return calculated bonus points
        return (int) (basePoints * bonusMultiplier);
    }

    /**
     * Notifies all score listeners of a combo achievement.
     *
     * @param comboCount  Current combo count
     * @param bonusPoints Bonus points from combo
     */
    private void notifyComboListeners(int comboCount, int bonusPoints) {
        for (ScoreListener listener : scoreListeners) {
            try {
                listener.onComboScore(comboCount, bonusPoints);
            } catch (Exception e) {
                System.err.println("Error notifying combo listener: " + e.getMessage());
            }
        }
    }

    /**
     * Gets the current score formatted with commas.
     *
     * @return Formatted score string (e.g., "1,234,567")
     */
    public String getFormattedScore() {
        // Use scoreFormatter to format currentScore with commas
        return scoreFormatter.format(currentScore);
    }

    /**
     * Gets a formatted score string for any score value.
     *
     * @param score Score value to format
     * @return Formatted score string
     */
    public String getFormattedScore(int score) {
        // Use scoreFormatter to format provided score
        return scoreFormatter.format(score);
    }

    /**
     * Checks if the current score qualifies as a high score.
     *
     * @return true if current score is a high score
     */
    public boolean isHighScore() {
        // If highScores list is not full (< maxHighScores), return true
        if (highScores.size() < maxHighScores) {
            return true;
        }

        // If list is full, compare currentScore with lowest high score
        if (!highScores.isEmpty()) {
            HighScore lowestHighScore = highScores.get(highScores.size() - 1);
            return currentScore > lowestHighScore.score;
        }

        return false;
    }

    /**
     * Saves the current score as a high score.
     *
     * @param playerName Name of the player achieving the high score
     * @return The rank of the new high score (1-based)
     */
    public int saveHighScore(String playerName) {
        if (playerName == null || playerName.trim().isEmpty()) {
            playerName = "Anonymous";
        }

        // Create new HighScore entry with playerName, currentScore, current time
        HighScore newHighScore = new HighScore(playerName.trim(), currentScore, System.currentTimeMillis());

        // Insert into highScores list in correct position (sorted by score)
        int rank = 1;
        boolean inserted = false;

        for (int i = 0; i < highScores.size(); i++) {
            if (newHighScore.score > highScores.get(i).score) {
                highScores.add(i, newHighScore);
                rank = i + 1;
                inserted = true;
                break;
            }
        }

        // If not inserted yet, add to end
        if (!inserted) {
            highScores.add(newHighScore);
            rank = highScores.size();
        }

        // Trim list to maxHighScores if necessary
        if (highScores.size() > maxHighScores) {
            highScores = highScores.subList(0, maxHighScores);
        }

        // Save high scores to file with saveHighScores()
        saveHighScores();

        // Notify listeners of high score achievement
        notifyHighScoreListeners(currentScore, rank);

        // Return rank of new high score
        return rank;
    }

    /**
     * Loads high scores from persistent storage.
     */
    private void loadHighScores() {
        try {
            // Try to read highScoreFile
            File file = new File(highScoreFile);
            if (!file.exists()) {
                // Create empty high score list if file doesn't exist
                System.out.println("High score file not found, starting with empty list");
                return;
            }

            try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // Parse high score data (handle different formats)
                    String[] parts = line.split(",");
                    if (parts.length >= 3) {
                        String name = parts[0].trim();
                        int score = Integer.parseInt(parts[1].trim());
                        long timestamp = Long.parseLong(parts[2].trim());

                        highScores.add(new HighScore(name, score, timestamp));
                    }
                }
            }

            // Sort by score in descending order
            highScores.sort((a, b) -> Integer.compare(b.score, a.score));

            System.out.println("Loaded " + highScores.size() + " high scores");

        } catch (Exception e) {
            // Handle file not found or corruption gracefully
            System.err.println("Error loading high scores: " + e.getMessage());
            highScores.clear(); // Create empty high score list
        }
    }

    /**
     * Saves high scores to persistent storage.
     */
    private void saveHighScores() {
        try {
            // Create or overwrite highScoreFile
            try (PrintWriter writer = new PrintWriter(new FileWriter(highScoreFile))) {
                // Write high score data in readable format
                // Include player name, score, and timestamp
                for (HighScore highScore : highScores) {
                    writer.println(highScore.playerName + "," + highScore.score + "," + highScore.timestamp);
                }
            }
            System.out.println("High scores saved to " + highScoreFile);
        } catch (IOException e) {
            // Handle file I/O errors gracefully
            System.err.println("Error saving high scores: " + e.getMessage());
        }
    }

    /**
     * Notifies all score listeners of a high score achievement.
     *
     * @param score The high score achieved
     * @param rank  The rank of the high score (1-based)
     */
    private void notifyHighScoreListeners(int score, int rank) {
        for (ScoreListener listener : scoreListeners) {
            try {
                listener.onHighScore(score, rank);
            } catch (Exception e) {
                System.err.println("Error notifying high score listener: " + e.getMessage());
            }
        }
    }

    /**
     * Resets the current score to zero.
     * Used when starting a new game.
     */
    public void resetScore() {
        // Set currentScore = 0
        this.currentScore = 0;

        // Keep sessionScore for statistics (don't reset)

        // Reset scoreMultiplier = 1.0
        this.scoreMultiplier = 1.0;

        // Reset comboCount = 0
        this.comboCount = 0;

        // Clear lastScoreTime
        this.lastScoreTime = 0;

        // Notify listeners of score reset
        notifyScoreListeners(0);
    }

    /**
     * Resets session statistics but keeps high scores.
     */
    public void resetSession() {
        // Set sessionScore = 0
        this.sessionScore = 0;

        // Keep currentScore and high scores (don't reset)

        // Reset combo and multiplier state
        this.scoreMultiplier = 1.0;
        this.comboCount = 0;
        this.lastScoreTime = 0;
    }

    /**
     * Sets the point value for a specific item type.
     *
     * @param itemType Type of item (pellet, power_pellet, ghost, etc.)
     * @param points   Point value for the item
     */
    public void setItemValue(String itemType, int points) {
        if (itemType == null) return;

        // Validate points >= 0
        if (points < 0) {
            throw new IllegalArgumentException("Point value cannot be negative");
        }

        // Store in scoreValues map
        // Allow overriding existing values
        scoreValues.put(itemType, points);
        System.out.println("Set " + itemType + " value to " + points + " points");
    }

    /**
     * Gets the point value for a specific item type.
     *
     * @param itemType Type of item to check
     * @return Point value, or 0 if item type not found
     */
    public int getItemValue(String itemType) {
        if (itemType == null) return 0;

        // Return value from scoreValues map
        // Return 0 if itemType not found
        return scoreValues.getOrDefault(itemType, 0);
    }

    /**
     * Adds a score event listener.
     *
     * @param listener The listener to add
     */
    public void addScoreListener(ScoreListener listener) {
        // Validate listener is not null
        if (listener == null) return;

        // Add listener to scoreListeners list
        // Allow duplicate listeners
        scoreListeners.add(listener);
    }

    /**
     * Removes a score event listener.
     *
     * @param listener The listener to remove
     */
    public void removeScoreListener(ScoreListener listener) {
        // Remove listener from scoreListeners list
        // Handle case where listener is not in list (remove() handles this gracefully)
        scoreListeners.remove(listener);
    }

    /**
     * Notifies all score listeners of a score change.
     *
     * @param scoreGained Points gained in this scoring event
     */
    private void notifyScoreListeners(int scoreGained) {
        // Iterate through scoreListeners list
        for (ScoreListener listener : scoreListeners) {
            try {
                // Call onScoreChanged() for each listener
                listener.onScoreChanged(currentScore, scoreGained);
            } catch (Exception e) {
                // Handle listener exceptions gracefully
                System.err.println("Error notifying score listener: " + e.getMessage());
            }
        }
    }

    // Getters
    public int getCurrentScore() {
        return currentScore;
    }

    public int getSessionScore() {
        return sessionScore;
    }

    public double getScoreMultiplier() {
        return scoreMultiplier;
    }

    public int getComboCount() {
        return comboCount;
    }

    public List<HighScore> getHighScores() {
        return new ArrayList<>(highScores);
    }

    /**
     * Gets scoring statistics.
     *
     * @return Map containing scoring metrics and statistics
     */
    public Map<String, Object> getScoreStats() {
        // Create map with scoring metrics
        Map<String, Object> stats = new HashMap<>();

        // Basic scoring information
        stats.put("currentScore", currentScore);
        stats.put("sessionScore", sessionScore);
        stats.put("formattedScore", getFormattedScore());

        // Multiplier and combo information
        stats.put("scoreMultiplier", scoreMultiplier);
        stats.put("comboCount", comboCount);
        stats.put("lastScoreTime", lastScoreTime);

        // High score information
        stats.put("isHighScore", isHighScore());
        stats.put("highScoreCount", highScores.size());
        stats.put("maxHighScores", maxHighScores);
        if (!highScores.isEmpty()) {
            stats.put("topHighScore", highScores.get(0).score);
            stats.put("lowestHighScore", highScores.get(highScores.size() - 1).score);
        }

        // Item values and scoring events
        stats.put("itemValues", new HashMap<>(scoreValues));
        stats.put("listenerCount", scoreListeners.size());

        // Return statistics map
        return stats;
    }
}