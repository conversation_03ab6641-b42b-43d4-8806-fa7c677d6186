package managers;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;

import components.Position;
import entities.Entity;
import entities.Ghost;
import entities.Player;
import tileengine.TETile;

/**
 * Entity lifecycle management and collision detection system.
 * Manages all game entities, handles updates, and provides collision detection.
 * <p>
 * This manager provides:
 * - Entity creation, registration, and removal
 * - Batch entity updates with proper order
 * - Efficient collision detection using spatial partitioning
 * - Entity queries and filtering
 * - Safe concurrent entity modification
 */
public class EntityManager {
    // Entity storage
    private Map<String, Entity> entities;
    private List<Entity> entityList; // For ordered iteration

    // Entities to add/remove next frame (thread-safe modification)
    private Queue<Entity> entitiesToAdd;
    private Queue<String> entitiesToRemove;

    // Spatial partitioning for efficient collision detection
    private Map<String, List<Entity>> spatialGrid;
    private int gridCellSize;

    // Entity type collections for quick filtering
    private List<Player> players;
    private List<Ghost> ghosts;
    private List<Entity> dynamicEntities; // Entities that move
    private List<Entity> staticEntities;  // Entities that don't move

    // World reference for collision detection
    private TETile[][] world;

    // Performance tracking
    private int totalEntities;
    private long lastUpdateTime;

    /**
     * Creates a new EntityManager with default settings.
     */
    public EntityManager() {
        // - Initialize all collections (HashMap, ArrayList, ConcurrentLinkedQueue)
        entities = new HashMap<>();
        entityList = new ArrayList<>();
        entitiesToAdd = new ConcurrentLinkedQueue<>();
        entitiesToRemove = new ConcurrentLinkedQueue<>();
        players = new ArrayList<>();
        ghosts = new ArrayList<>();
        dynamicEntities = new ArrayList<>();
        staticEntities = new ArrayList<>();
        world = null;
        // - Set gridCellSize to reasonable default (e.g., 4 tiles)
        gridCellSize = 4;
        // - Initialize spatial grid
        spatialGrid = new HashMap<>();
        // - Initialize performance tracking variables
        totalEntities = 0;
        lastUpdateTime = 0;
    }

    /**
     * Sets the world reference for collision detection.
     * Must be called before collision detection can work properly.
     *
     * @param world The 2D world array for collision checking
     */
    public void setWorld(TETile[][] world) {
        // - Store world reference
        if (world == null) {
            throw new IllegalArgumentException("World cannot be null");
        }
        this.world = world;
        // - Update all existing entities with world reference
        for (Entity entity : entities.values()) {
            entity.setWorld(world);
        }
        // - Validate world dimensions are reasonable
        if (world.length == 0 || world[0].length == 0) {
            throw new IllegalArgumentException("World dimensions cannot be zero");
        }
    }

    /**
     * Adds an entity to be managed by this system.
     * Entity will be added at the start of the next update cycle.
     *
     * @param entity The entity to add
     */
    public void addEntity(Entity entity) {
        // - Validate entity is not null
        if (entity == null) {
            throw new IllegalArgumentException("Entity cannot be null");
        }
        // - Add entity to entitiesToAdd queue
        entitiesToAdd.add(entity);
    }

    /**
     * Removes an entity by ID.
     * Entity will be removed at the end of the next update cycle.
     *
     * @param entityId The ID of the entity to remove
     */
    public void removeEntity(String entityId) {
        // - Validate entityId exists
        if (!entities.containsKey(entityId)) {
            throw new IllegalArgumentException("Entity with ID " + entityId + " not found");
        }
        // - Add entityId to entitiesToRemove queue
        entitiesToRemove.add(entityId);
    }

    /**
     * Gets an entity by its ID.
     *
     * @param entityId The ID of the entity to retrieve
     * @return The entity, or null if not found
     */
    public Entity getEntity(String entityId) {
        // - Handle null/invalid IDs gracefully
        if (entityId == null) {
            throw new IllegalArgumentException("Entity ID cannot be null");
        }
        // - Return entity from entities map
        if (entities.containsKey(entityId)) {
            return entities.get(entityId);
        }
        return null;
    }

    /**
     * Updates all managed entities.
     * Should be called once per frame during the game update cycle.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    public void updateAll(double deltaTime) {
        // - Process pending additions and removals
        processPendingChanges();
        // - Update spatial grid positions
        updateSpatialGrid();

        // - Update all entities in proper order:
        //   1. Players first (input processing)
        for (Player player : players) {
            player.update(deltaTime);
        }

        //   2. Dynamic entities (AI, movement)
        for (Ghost ghost : ghosts) {
            ghost.update(deltaTime);
        }

        //   3. Static entities (if needed)

        // - Check collisions after movement updates
        checkCollisions();

        // - Update performance tracking
        lastUpdateTime = System.currentTimeMillis();
        totalEntities = entities.size();
    }

    /**
     * Processes pending entity additions and removals.
     * Called at the beginning of each update cycle.
     */
    private void processPendingChanges() {
        // - Process entitiesToAdd queue:
        //   - Add entities to main storage
        //   - Add to appropriate type collections
        //   - Initialize entity with world reference
        //   - Add to spatial grid
        while (!entitiesToAdd.isEmpty()) {
            Entity entity = entitiesToAdd.poll();
            entities.put(entity.getEntityId(), entity);
            entityList.add(entity);
            if (entity instanceof Player) {
                players.add((Player) entity);
            } else if (entity instanceof Ghost) {
                ghosts.add((Ghost) entity);
            }
            if (entity.canMove()) {
                dynamicEntities.add(entity);
            } else {
                staticEntities.add(entity);
            }
            entity.setWorld(world);
            totalEntities++;
        }
        // - Process entitiesToRemove queue:
        //   - Remove from all collections
        //   - Remove from spatial grid
        //   - Clean up entity resources
        // - Update totalEntities count
        while (!entitiesToRemove.isEmpty()) {
            String entityId = entitiesToRemove.poll();
            Entity entity = entities.remove(entityId);
            if (entity instanceof Player) {
                players.remove(entity);
            } else if (entity instanceof Ghost) {
                ghosts.remove(entity);
            }
            if (entity.canMove()) {
                dynamicEntities.remove(entity);
            } else {
                staticEntities.remove(entity);
            }
            entityList.remove(entity);
            entity.destroy();
            totalEntities--;
        }
    }

    /**
     * Updates the spatial grid with current entity positions.
     * Used for efficient collision detection and spatial queries.
     */
    private void updateSpatialGrid() {
        // - Clear spatial grid
        spatialGrid.clear();
        // - For each entity:
        //   - Calculate grid cell from entity position
        //   - Add entity to appropriate grid cell(s)
        //   - Handle entities spanning multiple cells if needed
        for (Entity entity : entities.values()) {
            int cellX = entity.getPosition().tileX() / gridCellSize;
            int cellY = entity.getPosition().tileY() / gridCellSize;
            String cellKey = cellX + "," + cellY;
            if (!spatialGrid.containsKey(cellKey)) {
                spatialGrid.put(cellKey, new ArrayList<>());
            }
            spatialGrid.get(cellKey).add(entity);
        }
    }

    /**
     * Checks for collisions between all entities.
     * Uses spatial partitioning for efficiency.
     */
    public void checkCollisions() {
        // Use spatial grid to find potential collision pairs
        for (List<Entity> entitiesInCell : spatialGrid.values()) {
            if (entitiesInCell.size() < 2) {
                continue; // No collisions possible with less than 2 entities
            }

            // Check collision between each pair of entities in the cell
            for (int i = 0; i < entitiesInCell.size(); i++) {
                for (int j = i + 1; j < entitiesInCell.size(); j++) {
                    Entity entity1 = entitiesInCell.get(i);
                    Entity entity2 = entitiesInCell.get(j);

                    // Skip if either entity is inactive
                    if (!entity1.isActive() || !entity2.isActive()) {
                        continue;
                    }

                    // Check if entities are colliding
                    if (areEntitiesColliding(entity1, entity2)) {
                        handleCollision(entity1, entity2);
                    }
                }
            }
        }
    }

    /**
     * Checks if two entities are colliding.
     *
     * @param entity1 First entity
     * @param entity2 Second entity
     * @return true if entities are colliding
     */
    private boolean areEntitiesColliding(Entity entity1, Entity entity2) {
        // Get positions of both entities
        Position pos1 = entity1.getPosition();
        Position pos2 = entity2.getPosition();

        if (pos1 == null || pos2 == null) {
            return false;
        }

        // Calculate distance between entities
        double distance = pos1.distanceTo(pos2);

        // Compare with sum of collision radii
        double combinedRadius = entity1.getCollisionRadius() + entity2.getCollisionRadius();

        // Return true if collision detected
        return distance <= combinedRadius;
    }

    /**
     * Handles collision between two entities.
     * Triggers appropriate collision responses.
     *
     * @param entity1 First colliding entity
     * @param entity2 Second colliding entity
     */
    private void handleCollision(Entity entity1, Entity entity2) {
        // Trigger collision events for both entities
        // Each entity handles its own collision response
        entity1.onCollision(entity2);
        entity2.onCollision(entity1);
    }

    /**
     * Gets all entities within a specified radius of a position.
     *
     * @param position The center position to search around
     * @param radius   The search radius in tiles
     * @return List of entities within the radius
     */
    public List<Entity> getEntitiesInRadius(Position position, double radius) {
        if (position == null) {
            throw new IllegalArgumentException("Position cannot be null");
        }
        if (radius < 0) {
            throw new IllegalArgumentException("Radius cannot be negative");
        }

        List<Entity> entitiesInRadius = new ArrayList<>();
        double radiusSquared = radius * radius; // Use squared distance for efficiency

        // Use spatial grid to narrow search area
        int centerCellX = position.tileX() / gridCellSize;
        int centerCellY = position.tileY() / gridCellSize;
        int cellRadius = (int) Math.ceil(radius / gridCellSize);

        // Check cells within the radius
        for (int dx = -cellRadius; dx <= cellRadius; dx++) {
            for (int dy = -cellRadius; dy <= cellRadius; dy++) {
                String cellKey = (centerCellX + dx) + "," + (centerCellY + dy);
                List<Entity> cellEntities = spatialGrid.get(cellKey);

                if (cellEntities != null) {
                    // Calculate distance from position to each candidate entity
                    for (Entity entity : cellEntities) {
                        if (entity.isActive()) {
                            double distanceSquared = position.distanceSquaredTo(entity.getPosition());
                            if (distanceSquared <= radiusSquared) {
                                entitiesInRadius.add(entity);
                            }
                        }
                    }
                }
            }
        }

        return entitiesInRadius;
    }

    /**
     * Gets all entities of a specific type.
     *
     * @param entityClass The class/type of entities to find
     * @return List of entities of the specified type
     */
    @SuppressWarnings("unchecked")
    public <T extends Entity> List<T> getEntitiesOfType(Class<T> entityClass) {
        if (entityClass == null) {
            throw new IllegalArgumentException("Entity class cannot be null");
        }

        List<T> result = new ArrayList<>();

        // Use pre-maintained type collections for common types
        if (entityClass == Player.class) {
            return (List<T>) new ArrayList<>(players);
        } else if (entityClass == Ghost.class) {
            return (List<T>) new ArrayList<>(ghosts);
        }

        // For other types, filter main entity list
        for (Entity entity : entities.values()) {
            if (entityClass.isInstance(entity)) {
                result.add((T) entity);
            }
        }

        return result;
    }

    /**
     * Gets the first player entity (assuming single player game).
     *
     * @return The player entity, or null if no player exists
     */
    public Player getPlayer() {
        // Return first player from players list
        if (players.isEmpty()) {
            return null; // Handle case where no player exists
        }
        return players.get(0);
    }

    /**
     * Gets all ghost entities.
     *
     * @return List of all ghost entities
     */
    public List<Ghost> getGhosts() {
        // - Return copy of ghosts list to prevent external modification
        return new ArrayList<>(ghosts);
    }

    /**
     * Clears all entities from the manager.
     * Used when starting a new game or resetting state.
     */
    public void clear() {
        // Destroy all existing entities before clearing
        for (Entity entity : entities.values()) {
            entity.destroy();
        }

        // Clear all entity collections
        entities.clear();
        entityList.clear();
        players.clear();
        ghosts.clear();
        dynamicEntities.clear();
        staticEntities.clear();

        // Clear spatial grid
        spatialGrid.clear();

        // Clear pending addition/removal queues
        entitiesToAdd.clear();
        entitiesToRemove.clear();

        // Reset counters and tracking variables
        totalEntities = 0;
        lastUpdateTime = 0;
    }

    /**
     * Gets the total number of managed entities.
     *
     * @return The current entity count
     */
    public int getEntityCount() {
        return totalEntities;
    }

    /**
     * Gets performance statistics for the entity system.
     *
     * @return Map containing performance metrics
     */
    public Map<String, Object> getPerformanceStats() {
        Map<String, Object> stats = new HashMap<>();

        // Total entities
        stats.put("totalEntities", totalEntities);

        // Entities by type
        stats.put("playerCount", players.size());
        stats.put("ghostCount", ghosts.size());
        stats.put("dynamicEntities", dynamicEntities.size());
        stats.put("staticEntities", staticEntities.size());

        // Last update time
        stats.put("lastUpdateTime", lastUpdateTime);

        // Spatial grid efficiency
        stats.put("spatialGridCells", spatialGrid.size());
        stats.put("gridCellSize", gridCellSize);

        // Pending operations
        stats.put("pendingAdditions", entitiesToAdd.size());
        stats.put("pendingRemovals", entitiesToRemove.size());

        return stats;
    }

    /**
     * Validates the internal state of the EntityManager.
     * Used for debugging and testing.
     *
     * @return true if state is consistent, false if corruption detected
     */
    public boolean validateState() {
        try {
            // Check that entity counts match across collections
            if (entities.size() != entityList.size()) {
                return false;
            }

            if (totalEntities != entities.size()) {
                return false;
            }

            // Check that type collections contain correct entities
            int expectedDynamic = 0;
            int expectedStatic = 0;
            int expectedPlayers = 0;
            int expectedGhosts = 0;

            for (Entity entity : entities.values()) {
                if (entity == null) {
                    return false; // Null entity reference
                }

                if (entity.canMove()) {
                    expectedDynamic++;
                } else {
                    expectedStatic++;
                }

                if (entity instanceof Player) {
                    expectedPlayers++;
                } else if (entity instanceof Ghost) {
                    expectedGhosts++;
                }
            }

            if (dynamicEntities.size() != expectedDynamic ||
                    staticEntities.size() != expectedStatic ||
                    players.size() != expectedPlayers ||
                    ghosts.size() != expectedGhosts) {
                return false;
            }

            // Check for duplicate IDs
            Set<String> seenIds = new HashSet<>();
            for (Entity entity : entities.values()) {
                String id = entity.getEntityId();
                if (seenIds.contains(id)) {
                    return false; // Duplicate ID found
                }
                seenIds.add(id);
            }

            // Verify entity references are valid
            for (Entity entity : entityList) {
                if (!entities.containsKey(entity.getEntityId())) {
                    return false; // Entity in list but not in map
                }
            }

            return true; // All validations passed
        } catch (Exception e) {
            e.printStackTrace();
            return false; // Exception during validation indicates corruption
        }
    }
}