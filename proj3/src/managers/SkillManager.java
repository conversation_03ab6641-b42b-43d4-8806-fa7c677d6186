package managers;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import entities.Player;

/**
 * Power pellet and skill system manager with extensible design.
 * Manages player skills, power-ups, cooldowns, and skill effects.
 * <p>
 * This manager provides:
 * - Extensible skill registration system
 * - Power pellet effect management
 * - Skill cooldowns and duration tracking
 * - Active skill state management
 * - Future skill expansion framework
 */
public class SkillManager {
    // Registered skills
    private Map<String, Skill> registeredSkills;
    private Map<String, SkillInstance> activeSkills;

    // Skill cooldowns
    private Map<String, Long> skillCooldowns;
    private Map<String, Long> lastSkillUse;

    // Power pellet effects
    private boolean powerPelletActive;
    private double powerPelletTimeRemaining;
    private static final double POWER_PELLET_DURATION = 3.0; // From specification

    // Player reference
    private Player player;

    // Skill listeners for effects and UI
    private List<SkillListener> skillListeners;

    /**
     * Interface for skill effect implementations.
     */
    public interface Skill {
        String getId();

        String getName();

        String getDescription();

        double getCooldown(); // Cooldown in seconds

        double getDuration(); // Effect duration in seconds, 0 for instant

        boolean canActivate(Player player, SkillManager manager);

        void activate(Player player, SkillManager manager);

        void update(double deltaTime, Player player, SkillManager manager);

        void deactivate(Player player, SkillManager manager);
    }

    /**
     * Active skill instance tracking.
     */
    private static class SkillInstance {
        public final Skill skill;
        public double timeRemaining;
        public long activationTime;

        public SkillInstance(Skill skill, double duration) {
            this.skill = skill;
            this.timeRemaining = duration;
            this.activationTime = System.currentTimeMillis();
        }
    }

    /**
     * Interface for skill event listeners.
     */
    public interface SkillListener {
        void onSkillActivated(String skillId, double duration);

        void onSkillDeactivated(String skillId);

        void onSkillCooldownComplete(String skillId);

        void onPowerPelletActivated(double duration);

        void onPowerPelletExpired();
    }

    /**
     * Creates a SkillManager with default skills registered.
     */
    public SkillManager() {
        // Initialize all collections (HashMap, ArrayList)
        this.registeredSkills = new HashMap<>();
        this.activeSkills = new HashMap<>();
        this.skillCooldowns = new HashMap<>();
        this.lastSkillUse = new HashMap<>();
        this.skillListeners = new ArrayList<>();

        // Set powerPelletActive = false, powerPelletTimeRemaining = 0
        this.powerPelletActive = false;
        this.powerPelletTimeRemaining = 0.0;

        // Set player = null (will be set externally)
        this.player = null;

        // Register default skills with registerDefaultSkills()
        registerDefaultSkills();
    }

    /**
     * Creates a SkillManager with player reference.
     *
     * @param player The player this skill manager serves
     */
    public SkillManager(Player player) {
        // Call default constructor
        this();

        // Set this.player = player
        this.player = player;
    }

    /**
     * Registers default skills available in the game.
     */
    private void registerDefaultSkills() {
        // Register power pellet effect as a skill
        registerSkill(new PowerPelletSkill());

        // Register additional skills mentioned in specification
        // Could include: laser shooting, speed boost, invincibility, etc.
        // For now, we'll register a basic speed boost skill as an example
        registerSkill(new SpeedBoostSkill());

        System.out.println("Registered " + registeredSkills.size() + " default skills");
    }

    /**
     * Registers a new skill with the system.
     *
     * @param skill The skill implementation to register
     */
    public void registerSkill(Skill skill) {
        // Validate skill is not null and has valid ID
        if (skill == null) {
            System.err.println("Cannot register null skill");
            return;
        }

        String skillId = skill.getId();
        if (skillId == null || skillId.trim().isEmpty()) {
            System.err.println("Cannot register skill with null or empty ID");
            return;
        }

        // Check if skill ID already exists (warn but allow override)
        if (registeredSkills.containsKey(skillId)) {
            System.out.println("Warning: Overriding existing skill: " + skillId);
        }

        // Store in registeredSkills map
        registeredSkills.put(skillId, skill);

        // Initialize cooldown tracking for skill
        skillCooldowns.put(skillId, 0L);
        lastSkillUse.put(skillId, 0L);

        System.out.println("Registered skill: " + skillId + " (" + skill.getName() + ")");
    }

    /**
     * Default Power Pellet skill implementation.
     */
    private static class PowerPelletSkill implements Skill {
        @Override
        public String getId() {
            return "power_pellet";
        }

        @Override
        public String getName() {
            return "Power Pellet";
        }

        @Override
        public String getDescription() {
            return "Makes ghosts frightened and edible for a short time";
        }

        @Override
        public double getCooldown() {
            return 0.0;
        } // No cooldown for power pellets

        @Override
        public double getDuration() {
            return POWER_PELLET_DURATION;
        }

        @Override
        public boolean canActivate(Player player, SkillManager manager) {
            return !manager.isPowerPelletActive(); // Can't stack power pellets
        }

        @Override
        public void activate(Player player, SkillManager manager) {
            manager.activatePowerPellet();
        }

        @Override
        public void update(double deltaTime, Player player, SkillManager manager) {
            // Power pellet update is handled separately
        }

        @Override
        public void deactivate(Player player, SkillManager manager) {
            // Power pellet deactivation is handled separately
        }
    }

    /**
     * Default Speed Boost skill implementation.
     */
    private static class SpeedBoostSkill implements Skill {
        @Override
        public String getId() {
            return "speed_boost";
        }

        @Override
        public String getName() {
            return "Speed Boost";
        }

        @Override
        public String getDescription() {
            return "Temporarily increases movement speed";
        }

        @Override
        public double getCooldown() {
            return 10.0;
        } // 10 second cooldown

        @Override
        public double getDuration() {
            return 5.0;
        } // 5 second duration

        @Override
        public boolean canActivate(Player player, SkillManager manager) {
            return true; // Can always activate if not on cooldown
        }

        @Override
        public void activate(Player player, SkillManager manager) {
            // Increase player speed (placeholder - would need player speed modification)
            System.out.println("Speed boost activated!");
        }

        @Override
        public void update(double deltaTime, Player player, SkillManager manager) {
            // Maintain speed boost effect
        }

        @Override
        public void deactivate(Player player, SkillManager manager) {
            // Restore normal player speed
            System.out.println("Speed boost deactivated!");
        }
    }

    /**
     * Attempts to activate a skill by ID.
     *
     * @param skillId ID of the skill to activate
     * @param player  Player activating the skill
     * @return true if skill was successfully activated
     */
    public boolean activateSkill(String skillId, Player player) {
        if (skillId == null) return false;

        // Get skill from registeredSkills
        Skill skill = registeredSkills.get(skillId);
        if (skill == null) {
            System.err.println("Skill not found: " + skillId);
            return false;
        }

        // Check if skill can be activated:
        // - Not on cooldown
        if (isSkillOnCooldown(skillId)) {
            System.out.println("Skill " + skillId + " is on cooldown");
            return false;
        }

        // - Player meets requirements and skill's own canActivate() method returns true
        if (!skill.canActivate(player, this)) {
            System.out.println("Skill " + skillId + " cannot be activated");
            return false;
        }

        // If can activate:
        // - Call skill.activate()
        skill.activate(player, this);

        // - Add to activeSkills if has duration
        double duration = skill.getDuration();
        if (duration > 0) {
            activeSkills.put(skillId, new SkillInstance(skill, duration));
        }

        // - Set cooldown timer
        double cooldown = skill.getCooldown();
        if (cooldown > 0) {
            long cooldownEndTime = System.currentTimeMillis() + (long) (cooldown * 1000);
            skillCooldowns.put(skillId, cooldownEndTime);
        }
        lastSkillUse.put(skillId, System.currentTimeMillis());

        // - Notify listeners
        notifySkillActivated(skillId, duration);

        // - Return true
        return true;
    }

    /**
     * Activates the power pellet effect.
     * This is the primary skill mentioned in the specification.
     */
    public void activatePowerPellet() {
        // Set powerPelletActive = true
        this.powerPelletActive = true;

        // Set powerPelletTimeRemaining = POWER_PELLET_DURATION
        this.powerPelletTimeRemaining = POWER_PELLET_DURATION;

        // Notify player of power-up state (change appearance, etc.)
        System.out.println("Power pellet activated! Duration: " + POWER_PELLET_DURATION + " seconds");

        // Notify all ghosts to become frightened
        // (This would be handled by the game engine or entity manager)

        // Trigger power pellet sound effects
        // (This would be handled by the audio manager)

        // Notify listeners
        notifyPowerPelletActivated(POWER_PELLET_DURATION);
    }

    /**
     * Updates all active skills and manages durations.
     * Should be called every frame during game updates.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    public void updateSkills(double deltaTime) {
        // Update power pellet timer if active
        updatePowerPellet(deltaTime);

        // For each active skill:
        List<String> expiredSkills = new ArrayList<>();
        for (Map.Entry<String, SkillInstance> entry : activeSkills.entrySet()) {
            String skillId = entry.getKey();
            SkillInstance instance = entry.getValue();

            // Call skill.update()
            instance.skill.update(deltaTime, player, this);

            // Decrease timeRemaining
            instance.timeRemaining -= deltaTime;

            // If time expired, mark for deactivation
            if (instance.timeRemaining <= 0) {
                expiredSkills.add(skillId);
            }
        }

        // Deactivate expired skills
        for (String skillId : expiredSkills) {
            deactivateSkill(skillId);
        }

        // Update cooldown timers (cleanup expired cooldowns)
        long currentTime = System.currentTimeMillis();
        skillCooldowns.entrySet().removeIf(entry -> entry.getValue() <= currentTime);
    }

    /**
     * Updates power pellet effect duration.
     *
     * @param deltaTime Time elapsed since last update
     */
    private void updatePowerPellet(double deltaTime) {
        // If not powerPelletActive, return early
        if (!powerPelletActive) {
            return;
        }

        // Decrease powerPelletTimeRemaining by deltaTime
        powerPelletTimeRemaining -= deltaTime;

        // If time remaining <= 0:
        if (powerPelletTimeRemaining <= 0) {
            // Deactivate power pellet with deactivatePowerPellet()
            deactivatePowerPellet();
        }

        // Handle warning effects near expiration (blinking, etc.)
        if (powerPelletTimeRemaining <= 1.0 && powerPelletTimeRemaining > 0) {
            // Could trigger blinking or warning effects here
            // This would be handled by the rendering system
        }
    }

    /**
     * Deactivates the power pellet effect.
     */
    private void deactivatePowerPellet() {
        // Set powerPelletActive = false
        this.powerPelletActive = false;

        // Set powerPelletTimeRemaining = 0
        this.powerPelletTimeRemaining = 0.0;

        // Notify player to return to normal appearance
        System.out.println("Power pellet effect expired");

        // Notify all ghosts to return to normal behavior
        // (This would be handled by the game engine or entity manager)

        // Notify listeners of expiration
        notifyPowerPelletExpired();
    }

    /**
     * Deactivates a specific skill by ID.
     *
     * @param skillId ID of the skill to deactivate
     */
    public void deactivateSkill(String skillId) {
        if (skillId == null) return;

        // Find skill in activeSkills
        SkillInstance instance = activeSkills.get(skillId);
        if (instance != null) {
            // Call skill.deactivate()
            instance.skill.deactivate(player, this);

            // Remove from activeSkills
            activeSkills.remove(skillId);

            // Notify listeners
            notifySkillDeactivated(skillId);

            System.out.println("Deactivated skill: " + skillId);
        }
    }

    /**
     * Checks if a skill is currently on cooldown.
     *
     * @param skillId ID of the skill to check
     * @return true if skill is on cooldown
     */
    public boolean isSkillOnCooldown(String skillId) {
        if (skillId == null) return false;

        // Get cooldown expiration time from skillCooldowns
        Long cooldownEndTime = skillCooldowns.get(skillId);
        if (cooldownEndTime == null) return false;

        // Compare with current system time
        long currentTime = System.currentTimeMillis();

        // Return true if current time < expiration time
        return currentTime < cooldownEndTime;
    }

    /**
     * Gets the remaining cooldown time for a skill.
     *
     * @param skillId ID of the skill to check
     * @return Remaining cooldown time in seconds, or 0 if not on cooldown
     */
    public double getSkillCooldownRemaining(String skillId) {
        if (skillId == null) return 0.0;

        // Get cooldown expiration time from skillCooldowns
        Long cooldownEndTime = skillCooldowns.get(skillId);
        if (cooldownEndTime == null) return 0.0;

        // Calculate remaining time: (expiration - current time) / 1000
        long currentTime = System.currentTimeMillis();
        long remainingMs = cooldownEndTime - currentTime;

        // Return remaining time, or 0 if not on cooldown
        return Math.max(0.0, remainingMs / 1000.0);
    }

    /**
     * Checks if a skill is currently active.
     *
     * @param skillId ID of the skill to check
     * @return true if skill is currently active
     */
    public boolean isSkillActive(String skillId) {
        if (skillId == null) return false;

        // Check if skillId exists in activeSkills map
        return activeSkills.containsKey(skillId);
    }

    /**
     * Gets the remaining duration for an active skill.
     *
     * @param skillId ID of the skill to check
     * @return Remaining duration in seconds, or 0 if skill not active
     */
    public double getSkillTimeRemaining(String skillId) {
        if (skillId == null) return 0.0;

        // Get SkillInstance from activeSkills
        SkillInstance instance = activeSkills.get(skillId);

        // Return timeRemaining if found, 0 otherwise
        return instance != null ? Math.max(0.0, instance.timeRemaining) : 0.0;
    }

    /**
     * Gets a list of all registered skill IDs.
     *
     * @return List of skill IDs available for activation
     */
    public List<String> getAvailableSkills() {
        // Return list of keys from registeredSkills map
        // Return copy to prevent external modification
        return new ArrayList<>(registeredSkills.keySet());
    }

    /**
     * Gets a list of currently active skill IDs.
     *
     * @return List of skill IDs that are currently active
     */
    public List<String> getActiveSkills() {
        // Return list of keys from activeSkills map
        // Return copy to prevent external modification
        return new ArrayList<>(activeSkills.keySet());
    }

    /**
     * Gets information about a registered skill.
     *
     * @param skillId ID of the skill to get info for
     * @return Skill interface, or null if skill not found
     */
    public Skill getSkillInfo(String skillId) {
        if (skillId == null) return null;

        // Return skill from registeredSkills map
        // Return null if skillId not found
        return registeredSkills.get(skillId);
    }

    /**
     * Clears all active skills and resets cooldowns.
     * Used when starting a new game or resetting player state.
     */
    public void clearAllSkills() {
        // Deactivate all active skills properly
        List<String> activeSkillIds = new ArrayList<>(activeSkills.keySet());
        for (String skillId : activeSkillIds) {
            deactivateSkill(skillId);
        }

        // Clear activeSkills map (should already be empty from deactivateSkill calls)
        activeSkills.clear();

        // Clear cooldown timers
        skillCooldowns.clear();
        lastSkillUse.clear();

        // Deactivate power pellet if active
        if (powerPelletActive) {
            deactivatePowerPellet();
        }

        // Notify listeners of skill clearing (could add a specific event for this)
        System.out.println("All skills cleared");
    }

    /**
     * Adds a skill event listener.
     *
     * @param listener The listener to add
     */
    public void addSkillListener(SkillListener listener) {
        // Validate listener is not null
        if (listener == null) return;

        // Add listener to skillListeners list
        skillListeners.add(listener);
    }

    /**
     * Removes a skill event listener.
     *
     * @param listener The listener to remove
     */
    public void removeSkillListener(SkillListener listener) {
        // Remove listener from skillListeners list
        // Handle case where listener is not found (remove() handles this gracefully)
        skillListeners.remove(listener);
    }

    /**
     * Notifies all skill listeners of a skill activation.
     *
     * @param skillId  ID of activated skill
     * @param duration Duration of skill effect
     */
    private void notifySkillActivated(String skillId, double duration) {
        // Iterate through skillListeners
        for (SkillListener listener : skillListeners) {
            try {
                // Call onSkillActivated() for each listener
                listener.onSkillActivated(skillId, duration);
            } catch (Exception e) {
                // Handle listener exceptions gracefully
                System.err.println("Error notifying skill activation listener: " + e.getMessage());
            }
        }
    }

    /**
     * Notifies all skill listeners of a skill deactivation.
     *
     * @param skillId ID of deactivated skill
     */
    private void notifySkillDeactivated(String skillId) {
        for (SkillListener listener : skillListeners) {
            try {
                listener.onSkillDeactivated(skillId);
            } catch (Exception e) {
                System.err.println("Error notifying skill deactivation listener: " + e.getMessage());
            }
        }
    }

    /**
     * Notifies all skill listeners of power pellet activation.
     *
     * @param duration Duration of power pellet effect
     */
    private void notifyPowerPelletActivated(double duration) {
        for (SkillListener listener : skillListeners) {
            try {
                listener.onPowerPelletActivated(duration);
            } catch (Exception e) {
                System.err.println("Error notifying power pellet activation listener: " + e.getMessage());
            }
        }
    }

    /**
     * Notifies all skill listeners of power pellet expiration.
     */
    private void notifyPowerPelletExpired() {
        for (SkillListener listener : skillListeners) {
            try {
                listener.onPowerPelletExpired();
            } catch (Exception e) {
                System.err.println("Error notifying power pellet expiration listener: " + e.getMessage());
            }
        }
    }

    /**
     * Sets the player reference for skill effects.
     *
     * @param player The player this skill manager serves
     */
    public void setPlayer(Player player) {
        this.player = player;
    }

    // Power pellet state getters
    public boolean isPowerPelletActive() {
        return powerPelletActive;
    }

    public double getPowerPelletTimeRemaining() {
        return powerPelletTimeRemaining;
    }

    /**
     * Gets skill system statistics and status.
     *
     * @return Map containing skill system metrics
     */
    public Map<String, Object> getSkillStats() {
        // Create map with skill system metrics
        Map<String, Object> stats = new HashMap<>();

        // Number of registered/active skills
        stats.put("registeredSkillsCount", registeredSkills.size());
        stats.put("activeSkillsCount", activeSkills.size());
        stats.put("registeredSkills", getAvailableSkills());
        stats.put("activeSkills", getActiveSkills());

        // Power pellet state and time remaining
        stats.put("powerPelletActive", powerPelletActive);
        stats.put("powerPelletTimeRemaining", powerPelletTimeRemaining);
        stats.put("powerPelletDuration", POWER_PELLET_DURATION);

        // Cooldown information
        Map<String, Double> cooldowns = new HashMap<>();
        for (String skillId : registeredSkills.keySet()) {
            cooldowns.put(skillId, getSkillCooldownRemaining(skillId));
        }
        stats.put("skillCooldowns", cooldowns);

        // Skill usage statistics
        stats.put("listenerCount", skillListeners.size());
        stats.put("playerConnected", player != null);

        // Return statistics map
        return stats;
    }
}