package managers;

import tileengine.TETile;
import tileengine.Tileset;
import core.World;
import core.WorldType;
import core.HubWorld;
import core.LevelWorld;
import core.Room;
import data.WorldChunk;
import components.Position;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Random;

/**
 * Chunk-based world generation and infinite scrolling manager.
 * Manages world chunks, handles dynamic loading/unloading, and provides infinite scrolling.
 * <p>
 * This manager provides:
 * - Chunk-based world organization
 * - Dynamic chunk loading and unloading
 * - Infinite world scrolling
 * - World persistence and serialization
 * - Efficient memory management for large worlds
 */
public class WorldManager {
    // Chunk management
    private Map<String, WorldChunk> loadedChunks;
    private Map<String, WorldChunk> cachedChunks; // Recently unloaded chunks
    private int chunkSize; // Size of each chunk in tiles

    // World generation
    private World currentWorld;
    private WorldType currentWorldType;
    private Random worldRandom;
    private long worldSeed;

    // Active world area
    private TETile[][] activeWorld; // Current visible world area
    private int activeWorldWidth;
    private int activeWorldHeight;
    private Position cameraPosition; // Center of loaded area

    // Chunk loading parameters
    private int loadRadius; // Radius of chunks to keep loaded around camera
    private int cacheRadius; // Radius of chunks to keep cached
    private int maxLoadedChunks;
    private int maxCachedChunks;

    // Performance tracking
    private int chunksGenerated;
    private int chunksLoaded;
    private int chunksUnloaded;
    private long lastChunkUpdate;

    /**
     * Creates a WorldManager with default chunk settings.
     *
     * @param worldSeed    Seed for world generation
     * @param activeWidth  Width of active world area
     * @param activeHeight Height of active world area
     */
    public WorldManager(long worldSeed, int activeWidth, int activeHeight) {
        this.worldSeed = worldSeed;
        this.activeWorldWidth = activeWidth;
        this.activeWorldHeight = activeHeight;
        this.chunkSize = 50;
        this.loadRadius = 2;
        this.cacheRadius = 3;
        this.maxLoadedChunks = 25;
        this.maxCachedChunks = 50;
        
        // Initialize collections
        this.loadedChunks = new HashMap<>();
        this.cachedChunks = new HashMap<>();
        this.worldRandom = new Random(worldSeed);
        this.activeWorld = new TETile[activeWidth][activeHeight];
        this.cameraPosition = new Position(0, 0);
        
        // Initialize performance tracking
        this.chunksGenerated = 0;
        this.chunksLoaded = 0;
        this.chunksUnloaded = 0;
        this.lastChunkUpdate = System.currentTimeMillis();
        
        // No current world initially
        this.currentWorld = null;
        this.currentWorldType = null;
    }

    /**
     * Initializes the world manager and generates initial chunks.
     *
     * @param startPosition Starting camera position for initial chunk generation
     */
    public void initialize(Position startPosition) {
        // TODO: Initialize world manager
        // - Set cameraPosition = startPosition
        // - Generate initial chunks around start position
        // - Load visible chunks into activeWorld
        // - Set up chunk management timers
        throw new UnsupportedOperationException("WorldManager initialization implementation needed");
    }

    /**
     * Updates the world manager, handling chunk loading/unloading based on camera position.
     *
     * @param newCameraPosition Current camera position
     */
    public void update(Position newCameraPosition) {
        // TODO: Update world management
        // - Check if camera moved significantly since last update
        // - If moved: update cameraPosition and trigger chunk management
        // - Call manageChunks() to load/unload chunks as needed
        // - Update activeWorld with currently loaded chunks
        // - Clean up old cached chunks if memory usage is high
        throw new UnsupportedOperationException("WorldManager update implementation needed");
    }

    /**
     * Generates a new world chunk at the specified chunk coordinates.
     *
     * @param chunkX X coordinate of the chunk
     * @param chunkY Y coordinate of the chunk
     * @return Generated WorldChunk
     */
    public WorldChunk generateChunk(int chunkX, int chunkY) {
        // TODO: Generate world chunk
        // - Create new WorldChunk with specified coordinates
        // - Use worldGenerator to create chunk content:
        //   - Generate rooms and hallways within chunk boundaries
        //   - Connect chunk to neighboring chunks if they exist
        //   - Add appropriate entities (pellets, power pellets)
        // - Increment chunksGenerated counter
        // - Return generated chunk
        throw new UnsupportedOperationException("Chunk generation implementation needed");
    }

    /**
     * Loads a chunk and adds it to the active world.
     *
     * @param chunkX X coordinate of the chunk
     * @param chunkY Y coordinate of the chunk
     * @return The loaded WorldChunk
     */
    public WorldChunk loadChunk(int chunkX, int chunkY) {
        // TODO: Load world chunk
        // - Generate chunk key from coordinates
        // - Check if chunk already loaded (in loadedChunks)
        // - If not loaded:
        //   - Check cachedChunks first
        //   - If not cached, generate new chunk
        //   - Add to loadedChunks map
        //   - Integrate chunk into activeWorld
        //   - Increment chunksLoaded counter
        // - Return loaded chunk
        throw new UnsupportedOperationException("Chunk loading implementation needed");
    }

    /**
     * Unloads a chunk from active memory, optionally caching it.
     *
     * @param chunkX X coordinate of the chunk
     * @param chunkY Y coordinate of the chunk
     * @param cache  Whether to cache the chunk for quick reloading
     */
    public void unloadChunk(int chunkX, int chunkY, boolean cache) {
        // TODO: Unload world chunk
        // - Generate chunk key from coordinates
        // - Get chunk from loadedChunks
        // - If chunk exists:
        //   - Remove from activeWorld
        //   - Remove from loadedChunks
        //   - If cache is true and cache has space:
        //     - Add to cachedChunks
        //   - Otherwise clean up chunk resources
        //   - Increment chunksUnloaded counter
        throw new UnsupportedOperationException("Chunk unloading implementation needed");
    }

    /**
     * Gets a list of chunks that should be visible based on camera position.
     *
     * @return List of chunk coordinates that should be loaded
     */
    public List<String> getVisibleChunks() {
        // TODO: Calculate visible chunks
        // - Calculate chunk coordinates for camera position
        // - Generate list of chunk coordinates within loadRadius
        // - Consider activeWorld dimensions for visibility
        // - Return list of chunk keys that should be loaded
        throw new UnsupportedOperationException("Visible chunks calculation implementation needed");
    }

    /**
     * Manages chunk loading and unloading based on camera position.
     */
    private void manageChunks() {
        // TODO: Manage chunk loading/unloading
        // - Get list of visible chunks that should be loaded
        // - For each visible chunk not currently loaded:
        //   - Load chunk with loadChunk()
        // - For each loaded chunk no longer visible:
        //   - Unload chunk with unloadChunk() (cache if within cacheRadius)
        // - Limit total loaded chunks to maxLoadedChunks
        // - Clean up excess cached chunks if over maxCachedChunks
        throw new UnsupportedOperationException("Chunk management implementation needed");
    }

    /**
     * Updates the active world array with data from loaded chunks.
     */
    private void updateActiveWorld() {
        // TODO: Update active world
        // - Clear activeWorld array
        // - For each loaded chunk:
        //   - Copy chunk tiles to appropriate position in activeWorld
        //   - Handle chunk boundaries and transitions
        //   - Ensure seamless connections between chunks
        // - Fill empty areas with appropriate tiles (walls or void)
        throw new UnsupportedOperationException("Active world update implementation needed");
    }

    /**
     * Converts world coordinates to chunk coordinates.
     *
     * @param worldX World X coordinate
     * @param worldY World Y coordinate
     * @return Array containing [chunkX, chunkY]
     */
    public int[] worldToChunk(double worldX, double worldY) {
        // TODO: Convert world to chunk coordinates
        // - Calculate chunkX = floor(worldX / chunkSize)
        // - Calculate chunkY = floor(worldY / chunkSize)
        // - Handle negative coordinates correctly
        // - Return chunk coordinates as array
        throw new UnsupportedOperationException("World to chunk conversion implementation needed");
    }

    /**
     * Converts chunk coordinates to world coordinates (chunk origin).
     *
     * @param chunkX Chunk X coordinate
     * @param chunkY Chunk Y coordinate
     * @return Position of chunk's origin in world coordinates
     */
    public Position chunkToWorld(int chunkX, int chunkY) {
        // TODO: Convert chunk to world coordinates
        // - Calculate worldX = chunkX * chunkSize
        // - Calculate worldY = chunkY * chunkSize
        // - Return Position with calculated coordinates
        throw new UnsupportedOperationException("Chunk to world conversion implementation needed");
    }

    /**
     * Generates a chunk key from coordinates for map storage.
     *
     * @param chunkX Chunk X coordinate
     * @param chunkY Chunk Y coordinate
     * @return String key for chunk identification
     */
    private String generateChunkKey(int chunkX, int chunkY) {
        // TODO: Generate chunk key
        // - Create string key from coordinates (e.g., "x,y" format)
        // - Ensure key is unique and parseable
        // - Handle negative coordinates properly
        return chunkX + "," + chunkY;
    }

    /**
     * Saves all loaded chunks to persistent storage.
     *
     * @param saveDirectory Directory to save chunk data
     */
    public void saveChunks(String saveDirectory) {
        // TODO: Save chunks to storage
        // - For each loaded and cached chunk:
        //   - Serialize chunk data using chunk.serialize()
        //   - Write to file in saveDirectory
        //   - Use chunk key as filename
        // - Save chunk metadata (world seed, chunk size, etc.)
        // - Handle file I/O errors gracefully
        throw new UnsupportedOperationException("Chunk saving implementation needed");
    }

    /**
     * Loads chunks from persistent storage.
     *
     * @param saveDirectory Directory containing saved chunk data
     */
    public void loadChunks(String saveDirectory) {
        // TODO: Load chunks from storage
        // - Scan saveDirectory for chunk files
        // - For each chunk file:
        //   - Read and deserialize chunk data
        //   - Add to cachedChunks for quick access
        // - Load chunk metadata and validate compatibility
        // - Handle missing or corrupted chunk files
        throw new UnsupportedOperationException("Chunk loading from storage implementation needed");
    }

    /**
     * Gets the tile at specific world coordinates.
     *
     * @param worldX World X coordinate
     * @param worldY World Y coordinate
     * @return The tile at the specified position, or null if not loaded
     */
    public TETile getTileAt(double worldX, double worldY) {
        // TODO: Get tile at world position
        // - Convert world coordinates to activeWorld coordinates
        // - Check if coordinates are within activeWorld bounds
        // - Return tile from activeWorld array
        // - Return null or default tile if position not loaded
        throw new UnsupportedOperationException("Tile retrieval implementation needed");
    }

    /**
     * Sets the tile at specific world coordinates.
     *
     * @param worldX World X coordinate
     * @param worldY World Y coordinate
     * @param tile   The tile to place
     */
    public void setTileAt(double worldX, double worldY, TETile tile) {
        // TODO: Set tile at world position
        // - Convert world coordinates to chunk coordinates
        // - Ensure chunk is loaded
        // - Update tile in both chunk and activeWorld
        // - Mark chunk as modified for saving
        throw new UnsupportedOperationException("Tile setting implementation needed");
    }

    /**
     * Clears all chunks and resets the world manager.
     */
    public void clear() {
        // TODO: Clear world manager
        // - Clear all loaded and cached chunks
        // - Reset activeWorld array
        // - Reset performance counters
        // - Clear camera position
        throw new UnsupportedOperationException("World manager clearing implementation needed");
    }

    // Getters
    public TETile[][] getActiveWorld() {
        return activeWorld;
    }

    public int getActiveWorldWidth() {
        return activeWorldWidth;
    }

    public int getActiveWorldHeight() {
        return activeWorldHeight;
    }

    public int getChunkSize() {
        return chunkSize;
    }

    public Position getCameraPosition() {
        return cameraPosition;
    }

    public long getWorldSeed() {
        return worldSeed;
    }

    // Statistics
    public int getLoadedChunkCount() {
        return loadedChunks.size();
    }

    public int getCachedChunkCount() {
        return cachedChunks.size();
    }

    public int getChunksGenerated() {
        return chunksGenerated;
    }

    public int getChunksLoaded() {
        return chunksLoaded;
    }

    public int getChunksUnloaded() {
        return chunksUnloaded;
    }

    /**
     * Gets world management statistics.
     *
     * @return Map containing world management metrics
     */
    public Map<String, Object> getWorldStats() {
        // TODO: Collect world statistics
        // - Create map with world management metrics:
        //   - Loaded/cached chunk counts
        //   - Chunks generated/loaded/unloaded
        //   - Memory usage estimates
        //   - Camera position and active area
        // - Return statistics map
        throw new UnsupportedOperationException("World statistics collection implementation needed");
    }
    
    // ===============================
    // WORLD TYPE MANAGEMENT METHODS
    // ===============================
    
    /**
     * Sets the current world and determines management strategy based on world type.
     * 
     * @param world The world to manage
     */
    public void setCurrentWorld(World world) {
        this.currentWorld = world;
        this.currentWorldType = world.getWorldType();
        
        System.out.println("WorldManager now managing " + currentWorldType.getDisplayName());
        
        // Configure based on world type
        if (currentWorldType == WorldType.HUB_WORLD) {
            configureForHubWorld((HubWorld) world);
        } else if (currentWorldType == WorldType.LEVEL_WORLD) {
            configureForLevelWorld((LevelWorld) world);
        }
    }
    
    /**
     * Configures WorldManager for hub world management.
     * Hub worlds are fixed-size and don't need chunk loading.
     */
    private void configureForHubWorld(HubWorld hubWorld) {
        // Hub worlds are fixed size - no chunk management needed
        // Clear any existing chunks since we don't need them
        loadedChunks.clear();
        cachedChunks.clear();
        
        // Generate the hub world directly into active world array
        int[] dimensions = hubWorld.getDimensions();
        int hubWidth = Math.min(dimensions[0], activeWorldWidth);
        int hubHeight = Math.min(dimensions[1], activeWorldHeight);
        
        // Create temporary world array for generation
        TETile[][] hubWorldArray = new TETile[hubWidth][hubHeight];
        hubWorld.generateWorld(hubWorldArray);
        
        // Copy to active world array
        for (int x = 0; x < hubWidth && x < activeWorldWidth; x++) {
            for (int y = 0; y < hubHeight && y < activeWorldHeight; y++) {
                activeWorld[x][y] = hubWorldArray[x][y];
            }
        }
        
        System.out.println("Hub world loaded directly - no chunk management");
    }
    
    /**
     * Configures WorldManager for level world management.
     * Level worlds use chunk-based infinite scrolling.
     */
    private void configureForLevelWorld(LevelWorld levelWorld) {
        // Level worlds need full chunk management for infinite scrolling
        // Clear existing data and prepare for chunk-based generation
        loadedChunks.clear();
        cachedChunks.clear();
        chunksGenerated = 0;
        chunksLoaded = 0;
        chunksUnloaded = 0;
        
        // Set the level world as its own world manager reference
        levelWorld.setWorldManager(this);
        
        // Initialize chunks around starting position
        loadInitialChunks();
        
        System.out.println("Level world configured for chunk-based infinite scrolling");
    }
    
    /**
     * Updates world state based on current world type.
     * Different update strategies for hub vs level worlds.
     */
    public void updateWorld(double deltaTime) {
        if (currentWorld == null) return;
        
        if (currentWorldType == WorldType.HUB_WORLD) {
            // Hub world - minimal updates, no chunk management
            currentWorld.updateWorld(deltaTime);
        } else if (currentWorldType == WorldType.LEVEL_WORLD) {
            // Level world - full chunk management and updates
            currentWorld.updateWorld(deltaTime);
            // TODO: Implement chunk loading/unloading based on camera position
            // updateChunksAroundCamera();
        }
    }
    
    /**
     * Loads initial chunks for level world around the starting position.
     */
    private void loadInitialChunks() {
        // Generate initial chunks in a grid around camera position
        int startChunkX = (int) (cameraPosition.getX() / chunkSize) - loadRadius;
        int endChunkX = (int) (cameraPosition.getX() / chunkSize) + loadRadius;
        int startChunkY = (int) (cameraPosition.getY() / chunkSize) - loadRadius;
        int endChunkY = (int) (cameraPosition.getY() / chunkSize) + loadRadius;
        
        for (int chunkX = startChunkX; chunkX <= endChunkX; chunkX++) {
            for (int chunkY = startChunkY; chunkY <= endChunkY; chunkY++) {
                loadChunk(chunkX, chunkY);
            }
        }
    }
    
    /**
     * Loads a specific chunk for level worlds.
     */
//    private void loadChunk(int chunkX, int chunkY) {
//        String chunkKey = chunkX + "," + chunkY;
//
//        if (loadedChunks.containsKey(chunkKey)) {
//            return; // Already loaded
//        }
//
//        // Create new chunk
//        WorldChunk chunk = new WorldChunk(chunkX, chunkY, chunkSize);
//
//        // Generate chunk content based on current level world
//        if (currentWorld instanceof LevelWorld) {
//            generateLevelChunk(chunk, (LevelWorld) currentWorld);
//        }
//
//        loadedChunks.put(chunkKey, chunk);
//        chunksGenerated++;
//        chunksLoaded++;
//    }
    
    /**
     * Generates content for a level world chunk.
     */
    private void generateLevelChunk(WorldChunk chunk, LevelWorld levelWorld) {
        // TODO: Implement level-specific chunk generation
        // For now, create simple corridor structure
        TETile[][] chunkTiles = chunk.getTiles();
        
        // Fill with walls initially
        for (int x = 0; x < chunkSize; x++) {
            for (int y = 0; y < chunkSize; y++) {
                chunkTiles[x][y] = Tileset.WALL;
            }
        }
        
        // Create vertical corridors for Pac-Man gameplay
        for (int x = 5; x < chunkSize - 5; x += 10) {
            for (int y = 0; y < chunkSize; y++) {
                chunkTiles[x][y] = Tileset.FLOOR;
            }
        }
        
        // Add horizontal connections
        for (int y = 10; y < chunkSize; y += 15) {
            for (int x = 0; x < chunkSize; x++) {
                chunkTiles[x][y] = Tileset.FLOOR;
            }
        }
    }
    
    /**
     * Gets the current world being managed.
     */
    public World getCurrentWorld() {
        return currentWorld;
    }
    
    /**
     * Gets the current world type.
     */
    public WorldType getCurrentWorldType() {
        return currentWorldType;
    }
    
    /**
     * Checks if the current world supports infinite scrolling.
     */
    public boolean isInfiniteScrolling() {
        return currentWorldType != null && currentWorldType.isInfiniteScrolling();
    }
}