package managers;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Audio playback manager with looping and volume control.
 * Handles background music, sound effects, and audio state management.
 * <p>
 * This manager provides:
 * - Background music playback with looping
 * - Sound effect triggering and management
 * - Master and category volume control
 * - Audio state management (muted, paused)
 * - Audio asset integration with AssetManager
 */
public class AudioManager {
    // Audio categories for volume control
    public enum AudioCategory {
        MUSIC,          // Background music
        SOUND_EFFECTS,  // Game sound effects
        UI_SOUNDS,      // Menu and UI sounds
        VOICE           // Voice clips (if any)
    }

    // Audio state
    private boolean isMuted;
    private boolean isPaused;
    private double masterVolume;
    private Map<AudioCategory, Double> categoryVolumes;

    // Currently playing audio
    private Object currentMusic; // Placeholder for audio system integration
    private Map<String, Object> activeSounds; // Currently playing sound effects
    private List<String> loopingSounds; // Sounds that should loop

    // Audio asset management
    private AssetManager assetManager;
    private Map<String, String> audioAliases; // Friendly names for audio files

    // Audio system integration (placeholder)
    private Object audioSystem; // Will be actual audio system (Java Sound API, etc.)

    /**
     * Creates a new AudioManager with default settings.
     */
    public AudioManager() {
        // Set masterVolume = 1.0 (full volume)
        this.masterVolume = 1.0;

        // Initialize categoryVolumes map with default values (1.0 for all categories)
        this.categoryVolumes = new HashMap<>();
        for (AudioCategory category : AudioCategory.values()) {
            categoryVolumes.put(category, 1.0);
        }

        // Set isMuted = false, isPaused = false
        this.isMuted = false;
        this.isPaused = false;

        // Initialize activeSounds and loopingSounds collections
        this.activeSounds = new ConcurrentHashMap<>();
        this.loopingSounds = new ArrayList<>();

        // Initialize audioAliases map
        this.audioAliases = new HashMap<>();

        // Set assetManager = null (will be set externally)
        this.assetManager = null;

        // Initialize audio system placeholder
        this.audioSystem = null;
        this.currentMusic = null;
    }

    /**
     * Creates an AudioManager with asset manager integration.
     *
     * @param assetManager The asset manager for loading audio files
     */
    public AudioManager(AssetManager assetManager) {
        // Call default constructor
        this();

        // Set this.assetManager = assetManager
        this.assetManager = assetManager;

        // Initialize audio system integration
        initialize();
    }

    /**
     * Initializes the audio system.
     * Must be called before playing any audio.
     */
    public void initialize() {
        // Initialize Java Sound API or chosen audio library
        // For now, we'll use a simple placeholder system
        // In a real implementation, this would set up Java Sound API
        this.audioSystem = "AudioSystem_Initialized";

        // Set up audio output device (placeholder)
        // Configure audio format and sample rates (placeholder)

        // Test audio system functionality (placeholder)
        System.out.println("Audio system initialized successfully");

        // Load default audio aliases
        loadDefaultAliases();
    }

    /**
     * Loads default audio aliases for common sounds.
     */
    private void loadDefaultAliases() {
        // Background music aliases
        registerAlias("background", "background_music.wav");
        registerAlias("menu", "menu_music.wav");

        // Sound effect aliases
        registerAlias("pellet", "pellet_eat.wav");
        registerAlias("power_pellet", "power_pellet_eat.wav");
        registerAlias("ghost_eat", "ghost_eat.wav");
        registerAlias("death", "player_death.wav");
        registerAlias("level_complete", "level_complete.wav");
        registerAlias("game_over", "game_over.wav");

        // UI sound aliases
        registerAlias("menu_select", "menu_select.wav");
        registerAlias("menu_confirm", "menu_confirm.wav");
        registerAlias("button_click", "button_click.wav");
    }

    /**
     * Plays background music with looping.
     * Stops any currently playing music first.
     *
     * @param musicName The name/alias of the music to play
     * @param loop      Whether the music should loop indefinitely
     */
    public void playMusic(String musicName, boolean loop) {
        if (musicName == null) return;

        // Stop current music if playing
        stopMusic();

        try {
            // Resolve alias to actual filename
            String actualFileName = resolveAudioName(musicName);

            // Load music file through assetManager
            Object musicFile;
            if (assetManager != null) {
                musicFile = assetManager.loadAudio(actualFileName);
            } else {
                // Direct file loading fallback
                musicFile = "assets/audio/" + actualFileName; // Placeholder
            }

            if (musicFile != null) {
                // Start music playback with appropriate volume (placeholder implementation)
                // In real implementation, this would use Java Sound API
                this.currentMusic = musicFile;

                // Set looping if requested (placeholder)
                if (loop) {
                    System.out.println("Playing music: " + actualFileName + " (looping)");
                } else {
                    System.out.println("Playing music: " + actualFileName + " (once)");
                }
            }
        } catch (Exception e) {
            // Handle audio loading/playback errors gracefully
            System.err.println("Failed to play music: " + musicName + " - " + e.getMessage());
            this.currentMusic = null;
        }
    }

    /**
     * Plays background music with default looping enabled.
     *
     * @param musicName The name/alias of the music to play
     */
    public void playMusic(String musicName) {
        playMusic(musicName, true);
    }

    /**
     * Stops the currently playing background music.
     */
    public void stopMusic() {
        if (currentMusic != null) {
            // Stop currentMusic playback if exists (placeholder implementation)
            System.out.println("Stopping music");

            // Clean up music resources (placeholder)
            // In real implementation, this would stop the audio stream

            // Set currentMusic = null
            this.currentMusic = null;
        }
    }

    /**
     * Pauses the currently playing background music.
     */
    public void pauseMusic() {
        if (currentMusic != null && !isPaused) {
            // Pause currentMusic if playing (placeholder implementation)
            System.out.println("Pausing music");

            // Maintain position for resuming (placeholder)
            // In real implementation, this would pause the audio stream

            // Set music pause state
            this.isPaused = true;
        }
    }

    /**
     * Resumes paused background music.
     */
    public void resumeMusic() {
        if (currentMusic != null && isPaused) {
            // Resume currentMusic from paused position (placeholder implementation)
            System.out.println("Resuming music");

            // Restore volume and loop settings (placeholder)
            // In real implementation, this would resume the audio stream

            // Clear music pause state
            this.isPaused = false;
        }
    }

    /**
     * Plays a sound effect.
     *
     * @param soundName The name/alias of the sound to play
     * @param volume    Volume level (0.0 to 1.0, relative to category volume)
     * @param loop      Whether the sound should loop
     * @return Sound ID for controlling the sound, or null if failed
     */
    public String playSound(String soundName, double volume, boolean loop) {
        if (soundName == null) return null;

        try {
            // Resolve alias to actual filename
            String actualFileName = resolveAudioName(soundName);

            // Load sound file through assetManager
            Object soundFile;
            if (assetManager != null) {
                soundFile = assetManager.loadAudio(actualFileName);
            } else {
                // Direct file loading fallback
                soundFile = "assets/audio/" + actualFileName; // Placeholder
            }

            if (soundFile != null) {
                // Calculate final volume (masterVolume * categoryVolume * volume)
                double finalVolume = calculateVolume(volume, AudioCategory.SOUND_EFFECTS);

                // Generate unique sound ID for tracking
                String soundId = "sound_" + System.currentTimeMillis() + "_" + soundName;

                // Start sound playback (placeholder implementation)
                System.out.println("Playing sound: " + actualFileName + " at volume " + finalVolume +
                        (loop ? " (looping)" : ""));

                // Store in activeSounds map
                activeSounds.put(soundId, soundFile);

                // If loop is true, add to loopingSounds list
                if (loop) {
                    loopingSounds.add(soundId);
                }

                // Return sound ID for caller to control sound
                return soundId;
            }
        } catch (Exception e) {
            System.err.println("Failed to play sound: " + soundName + " - " + e.getMessage());
        }

        return null;
    }

    /**
     * Plays a sound effect with default volume and no looping.
     *
     * @param soundName The name/alias of the sound to play
     * @return Sound ID for controlling the sound
     */
    public String playSound(String soundName) {
        return playSound(soundName, 1.0, false);
    }

    /**
     * Stops a specific sound effect by ID.
     *
     * @param soundId The ID of the sound to stop
     */
    public void stopSound(String soundId) {
        if (soundId == null) return;

        // Find sound in activeSounds map
        Object sound = activeSounds.get(soundId);
        if (sound != null) {
            // Stop sound playback (placeholder implementation)
            System.out.println("Stopping sound: " + soundId);

            // Remove from activeSounds and loopingSounds
            activeSounds.remove(soundId);
            loopingSounds.remove(soundId);

            // Clean up sound resources (placeholder)
            // In real implementation, this would stop the audio stream
        }
    }

    /**
     * Stops all currently playing sound effects.
     */
    public void stopAllSounds() {
        // Iterate through activeSounds map
        for (String soundId : new ArrayList<>(activeSounds.keySet())) {
            // Stop each sound
            stopSound(soundId);
        }

        // Clear activeSounds and loopingSounds collections
        activeSounds.clear();
        loopingSounds.clear();

        System.out.println("All sounds stopped");
    }

    /**
     * Sets the master volume for all audio.
     *
     * @param volume Volume level (0.0 to 1.0)
     */
    public void setMasterVolume(double volume) {
        // Clamp volume to [0.0, 1.0] range
        this.masterVolume = Math.max(0.0, Math.min(1.0, volume));

        // Update volume for all currently playing audio (placeholder)
        // In real implementation, this would adjust the volume of active audio streams
        System.out.println("Master volume set to: " + this.masterVolume);

        // Apply volume change immediately to current music and sounds (placeholder)
        if (currentMusic != null) {
            System.out.println("Updating music volume");
        }
        if (!activeSounds.isEmpty()) {
            System.out.println("Updating " + activeSounds.size() + " active sounds volume");
        }
    }

    /**
     * Sets the volume for a specific audio category.
     *
     * @param category The audio category
     * @param volume   Volume level (0.0 to 1.0)
     */
    public void setCategoryVolume(AudioCategory category, double volume) {
        if (category == null) return;

        // Clamp volume to [0.0, 1.0] range
        double clampedVolume = Math.max(0.0, Math.min(1.0, volume));

        // Store in categoryVolumes map
        categoryVolumes.put(category, clampedVolume);

        // Update volume for all currently playing audio in category (placeholder)
        System.out.println("Category " + category + " volume set to: " + clampedVolume);

        // Apply volume change immediately (placeholder)
        // In real implementation, this would adjust volume of audio in this category
    }

    /**
     * Mutes all audio output.
     */
    public void mute() {
        // Set isMuted = true
        this.isMuted = true;

        // Reduce volume to 0 for all playing audio (placeholder)
        System.out.println("Audio muted");

        // Maintain original volume levels for unmuting
        // In real implementation, this would set audio stream volumes to 0
        // while preserving the original volume settings
    }

    /**
     * Unmutes audio output, restoring previous volume levels.
     */
    public void unmute() {
        // Set isMuted = false
        this.isMuted = false;

        // Restore original volume levels for all playing audio (placeholder)
        System.out.println("Audio unmuted");

        // Apply master and category volumes
        // In real implementation, this would restore audio stream volumes
        // using the preserved volume settings
    }

    /**
     * Pauses all audio playback.
     */
    public void pauseAll() {
        // Set isPaused = true
        this.isPaused = true;

        // Pause currentMusic if playing
        if (currentMusic != null) {
            System.out.println("Pausing all audio including music");
        }

        // Pause all active sound effects (placeholder)
        if (!activeSounds.isEmpty()) {
            System.out.println("Pausing " + activeSounds.size() + " active sounds");
        }

        // Maintain playback positions for resuming (placeholder)
        // In real implementation, this would pause all audio streams
    }

    /**
     * Resumes all paused audio.
     */
    public void resumeAll() {
        // Set isPaused = false
        this.isPaused = false;

        // Resume currentMusic if was playing
        if (currentMusic != null) {
            System.out.println("Resuming all audio including music");
        }

        // Resume all paused sound effects (placeholder)
        if (!activeSounds.isEmpty()) {
            System.out.println("Resuming " + activeSounds.size() + " active sounds");
        }

        // Restore volume levels (placeholder)
        // In real implementation, this would resume all audio streams
    }

    /**
     * Registers an audio alias for easier reference.
     *
     * @param alias    The friendly name for the audio
     * @param filename The actual filename of the audio asset
     */
    public void registerAlias(String alias, String filename) {
        if (alias == null || filename == null) return;

        // Store alias -> filename mapping in audioAliases
        audioAliases.put(alias, filename);

        // Validate that filename exists (optional - placeholder)
        // In real implementation, could check if file exists

        // Allow overriding existing aliases (already handled by map.put)
        System.out.println("Registered audio alias: " + alias + " -> " + filename);
    }

    /**
     * Resolves an audio alias to the actual filename.
     *
     * @param audioName The alias or filename to resolve
     * @return The actual filename, or the input if no alias found
     */
    private String resolveAudioName(String audioName) {
        if (audioName == null) return null;

        // Check if audioName exists in audioAliases map
        if (audioAliases.containsKey(audioName)) {
            // Return mapped filename if found
            return audioAliases.get(audioName);
        }

        // Return original audioName if no alias found
        return audioName;
    }

    /**
     * Calculates the final volume for audio playback.
     *
     * @param baseVolume The base volume level
     * @param category   The audio category
     * @return The final volume considering master and category volumes
     */
    private double calculateVolume(double baseVolume, AudioCategory category) {
        // If isMuted, return 0.0
        if (isMuted) {
            return 0.0;
        }

        // Get category volume from categoryVolumes map
        double categoryVolume = categoryVolumes.getOrDefault(category, 1.0);

        // Return masterVolume * categoryVolume * baseVolume
        double finalVolume = masterVolume * categoryVolume * baseVolume;

        // Clamp result to [0.0, 1.0] range
        return Math.max(0.0, Math.min(1.0, finalVolume));
    }

    /**
     * Checks if any audio is currently playing.
     *
     * @return true if music or sounds are playing
     */
    public boolean isPlaying() {
        // Check if currentMusic is playing
        boolean musicPlaying = (currentMusic != null && !isPaused);

        // Check if any sounds in activeSounds are playing
        boolean soundsPlaying = !activeSounds.isEmpty();

        // Return true if any audio is active
        return musicPlaying || soundsPlaying;
    }

    /**
     * Gets the current music being played.
     *
     * @return The name of current music, or null if none playing
     */
    public String getCurrentMusic() {
        // Return name/alias of currentMusic
        if (currentMusic != null) {
            // In a real implementation, this would extract the name from the audio object
            // For now, return a placeholder based on the currentMusic object
            return currentMusic.toString();
        }

        // Return null if no music playing
        return null;
    }

    /**
     * Gets a list of currently playing sound effects.
     *
     * @return List of sound IDs for active sounds
     */
    public List<String> getActiveSounds() {
        // Return list of keys from activeSounds map
        // Return copy to prevent external modification
        return new ArrayList<>(activeSounds.keySet());
    }

    /**
     * Cleans up audio resources and shuts down the audio system.
     * Should be called when the game is closing.
     */
    public void shutdown() {
        // Stop all playing audio
        stopMusic();
        stopAllSounds();

        // Clean up all audio resources (placeholder)
        System.out.println("Shutting down audio system");

        // Close audio system connections (placeholder)
        this.audioSystem = null;

        // Clear all collections
        activeSounds.clear();
        loopingSounds.clear();
        audioAliases.clear();
        categoryVolumes.clear();

        this.currentMusic = null;
        this.assetManager = null;
    }

    // Getters for audio state
    public boolean isMuted() {
        return isMuted;
    }

    public boolean isPaused() {
        return isPaused;
    }

    public double getMasterVolume() {
        return masterVolume;
    }

    public double getCategoryVolume(AudioCategory category) {
        return categoryVolumes.getOrDefault(category, 1.0);
    }

    /**
     * Gets audio system status information.
     *
     * @return Map containing audio system status
     */
    public Map<String, Object> getAudioStatus() {
        // Create map with audio system information
        Map<String, Object> status = new HashMap<>();

        // Basic audio state
        status.put("isMuted", isMuted);
        status.put("isPaused", isPaused);
        status.put("masterVolume", masterVolume);

        // Category volumes
        status.put("categoryVolumes", new HashMap<>(categoryVolumes));

        // Current music name
        status.put("currentMusic", getCurrentMusic());

        // Number of active sounds
        status.put("activeSoundsCount", activeSounds.size());
        status.put("loopingSoundsCount", loopingSounds.size());

        // Audio system health/status
        status.put("audioSystemInitialized", audioSystem != null);
        status.put("assetManagerConnected", assetManager != null);
        status.put("aliasesRegistered", audioAliases.size());

        // Return status map
        return status;
    }
}