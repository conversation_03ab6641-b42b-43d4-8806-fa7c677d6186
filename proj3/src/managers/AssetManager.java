package managers;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;

import tileengine.TETile;
import tileengine.Tileset;

/**
 * Sprite and audio asset loading with caching system.
 * Manages all game assets including sprites, audio files, and theme data.
 * <p>
 * This manager provides:
 * - Lazy loading of assets on demand
 * - Memory-efficient caching system
 * - Asset preloading for critical resources
 * - Error handling for missing assets
 * - Asset unloading and cleanup
 */
public class AssetManager {
    // Asset caches
    private Map<String, TETile> spriteCache;
    private Map<String, BufferedImage> imageCache;
    private Map<String, Object> audioCache; // Placeholder for audio system
    private Map<String, Map<String, Object>> themeCache;

    // Asset loading statistics
    private int totalAssetsLoaded;
    private int cacheHits;
    private int cacheMisses;
    private long totalLoadTime;

    // Configuration
    private boolean enableCaching;
    private int maxCacheSize;
    private String assetBasePath;

    // Asset categories for organization
    private static final String SPRITE_CATEGORY = "sprites";
    private static final String AUDIO_CATEGORY = "audio";
    private static final String THEME_CATEGORY = "themes";

    /**
     * Creates a new AssetManager with default settings.
     */
    public AssetManager() {
        // Initialize all cache maps (HashMap)
        spriteCache = new HashMap<>();
        imageCache = new HashMap<>();
        audioCache = new HashMap<>();
        themeCache = new HashMap<>();

        // Set enableCaching = true
        enableCaching = true;

        // Set maxCacheSize = 1000 (reasonable default)
        maxCacheSize = 1000;

        // Set assetBasePath = "assets/" (relative to project root)
        assetBasePath = "assets/";

        // Initialize statistics variables to 0
        totalAssetsLoaded = 0;
        cacheHits = 0;
        cacheMisses = 0;
        totalLoadTime = 0;
    }

    /**
     * Creates an AssetManager with custom configuration.
     *
     * @param assetBasePath Base path for asset loading
     * @param maxCacheSize  Maximum number of cached assets
     * @param enableCaching Whether to cache loaded assets
     */
    public AssetManager(String assetBasePath, int maxCacheSize, boolean enableCaching) {
        // Call default constructor for base initialization
        this();

        // Validate parameters (non-null paths, positive cache size)
        if (assetBasePath == null) {
            throw new IllegalArgumentException("Asset base path cannot be null");
        }
        if (maxCacheSize <= 0) {
            throw new IllegalArgumentException("Max cache size must be positive");
        }

        // Override settings with provided parameters
        this.assetBasePath = assetBasePath.endsWith("/") ? assetBasePath : assetBasePath + "/";
        this.maxCacheSize = maxCacheSize;
        this.enableCaching = enableCaching;
    }

    /**
     * Loads a sprite by name, using cache if available.
     *
     * @param spriteName The name/path of the sprite to load
     * @return The loaded TETile sprite, or default sprite if loading fails
     */
    public TETile loadSprite(String spriteName) {
        if (spriteName == null) {
            return getDefaultSprite();
        }

        // Check spriteCache for existing sprite
        if (enableCaching && spriteCache.containsKey(spriteName)) {
            // If found: increment cacheHits and return cached sprite
            cacheHits++;
            return spriteCache.get(spriteName);
        }

        // If not found: increment cacheMisses
        cacheMisses++;

        try {
            long startTime = System.currentTimeMillis();

            // Load sprite from file with loadSpriteFromFile()
            TETile sprite = loadSpriteFromFile(spriteName);

            long loadTime = System.currentTimeMillis() - startTime;
            totalLoadTime += loadTime;
            totalAssetsLoaded++;

            // Store in cache if enableCaching is true
            if (enableCaching) {
                // Manage cache size (remove oldest if exceeds maxCacheSize)
                if (spriteCache.size() >= maxCacheSize) {
                    optimizeCache();
                }
                spriteCache.put(spriteName, sprite);
            }

            return sprite;
        } catch (Exception e) {
            // Return fallback sprite if loading fails
            return getDefaultSprite();
        }
    }

    /**
     * Loads a sprite from file system.
     *
     * @param spriteName The sprite file name/path
     * @return The loaded TETile sprite
     */
    private TETile loadSpriteFromFile(String spriteName) {
        try {
            // Construct full file path: assetBasePath + SPRITE_CATEGORY + spriteName
            String fullPath = assetBasePath + SPRITE_CATEGORY + "/" + spriteName;

            // Try to load image file using ImageIO.read()
            BufferedImage image = ImageIO.read(new File(fullPath));
            if (image == null) {
                throw new IOException("Failed to load image: " + fullPath);
            }

            // Store image in cache for potential reuse
            if (enableCaching) {
                imageCache.put(spriteName, image);
            }

            // Convert BufferedImage to TETile (using filepath constructor)
            // Extract filename without extension for description
            String description = spriteName.replaceAll("\\.[^.]*$", "");

            // Create TETile with image filepath - TETile will handle image loading
            return new TETile(' ', java.awt.Color.WHITE, java.awt.Color.BLACK,
                    description, fullPath, spriteName.hashCode());

        } catch (IOException e) {
            // Handle file not found or invalid format exceptions
            throw new RuntimeException("Failed to load sprite: " + spriteName + " - " + e.getMessage(), e);
        }
    }

    /**
     * Loads an audio file by name, using cache if available.
     *
     * @param audioName The name/path of the audio file to load
     * @return The loaded audio object (placeholder for audio system integration)
     */
    public Object loadAudio(String audioName) {
        if (audioName == null) {
            return null;
        }

        // Check audioCache for existing audio
        if (enableCaching && audioCache.containsKey(audioName)) {
            // If found: increment cacheHits and return cached audio
            cacheHits++;
            return audioCache.get(audioName);
        }

        // If not found: increment cacheMisses
        cacheMisses++;

        try {
            long startTime = System.currentTimeMillis();

            // Load audio from file with loadAudioFromFile()
            Object audio = loadAudioFromFile(audioName);

            long loadTime = System.currentTimeMillis() - startTime;
            totalLoadTime += loadTime;
            totalAssetsLoaded++;

            // Store in cache if enableCaching is true
            if (enableCaching && audio != null) {
                if (audioCache.size() >= maxCacheSize) {
                    optimizeCache();
                }
                audioCache.put(audioName, audio);
            }

            return audio;
        } catch (Exception e) {
            // Return null if loading fails
            return null;
        }
    }

    /**
     * Loads audio from file system.
     *
     * @param audioName The audio file name/path
     * @return The loaded audio object
     */
    private Object loadAudioFromFile(String audioName) {
        // Construct full file path: assetBasePath + AUDIO_CATEGORY + audioName
        String fullPath = assetBasePath + AUDIO_CATEGORY + "/" + audioName;

        // Check if file exists
        File audioFile = new File(fullPath);
        if (!audioFile.exists()) {
            throw new RuntimeException("Audio file not found: " + fullPath);
        }

        // This is a placeholder - actual implementation depends on audio library
        // For now, return the file path as a string placeholder
        // In a real implementation, this would load the audio using Java Sound API
        // or another audio library like OpenAL, FMOD, etc.

        // Return file path as placeholder for audio object
        return fullPath;
    }

    /**
     * Gets an asset from cache without loading.
     *
     * @param assetName The name of the asset to retrieve
     * @param category  The category of asset (sprite, audio, theme)
     * @return The cached asset, or null if not found
     */
    public Object getAsset(String assetName, String category) {
        if (assetName == null || category == null) {
            return null;
        }

        // Switch on category to select appropriate cache
        switch (category.toLowerCase()) {
            case SPRITE_CATEGORY:
                if (spriteCache.containsKey(assetName)) {
                    cacheHits++;
                    return spriteCache.get(assetName);
                }
                break;
            case AUDIO_CATEGORY:
                if (audioCache.containsKey(assetName)) {
                    cacheHits++;
                    return audioCache.get(assetName);
                }
                break;
            case THEME_CATEGORY:
                if (themeCache.containsKey(assetName)) {
                    cacheHits++;
                    return themeCache.get(assetName);
                }
                break;
            default:
                // Unknown category
                return null;
        }

        // Return null if not found (don't attempt loading)
        cacheMisses++;
        return null;
    }

    /**
     * Preloads essential assets for immediate availability.
     * Should be called during game initialization.
     *
     * @param assetList List of asset names to preload
     */
    public void preloadAssets(List<String> assetList) {
        if (assetList == null || assetList.isEmpty()) {
            return;
        }

        int successCount = 0;
        int failureCount = 0;

        // For each asset in list:
        for (String assetName : assetList) {
            try {
                // Determine asset type from file extension or naming convention
                String extension = getFileExtension(assetName).toLowerCase();

                // Load asset using appropriate method (loadSprite, loadAudio)
                switch (extension) {
                    case "png":
                    case "jpg":
                    case "jpeg":
                    case "gif":
                    case "bmp":
                        loadSprite(assetName);
                        break;
                    case "wav":
                    case "mp3":
                    case "ogg":
                    case "aiff":
                        loadAudio(assetName);
                        break;
                    default:
                        // Try to load as sprite by default
                        loadSprite(assetName);
                        break;
                }
                successCount++;
            } catch (Exception e) {
                // Handle preloading errors gracefully (warn but don't fail)
                failureCount++;
                System.err.println("Warning: Failed to preload asset: " + assetName + " - " + e.getMessage());
            }
        }

        // Log preloading statistics
        System.out.println("Asset preloading completed: " + successCount + " successful, " + failureCount + " failed");
    }

    /**
     * Helper method to extract file extension from filename.
     */
    private String getFileExtension(String filename) {
        int lastDot = filename.lastIndexOf('.');
        return lastDot > 0 ? filename.substring(lastDot + 1) : "";
    }

    /**
     * Preloads all assets in a specific category.
     *
     * @param category The asset category to preload (sprites, audio, themes)
     */
    public void preloadCategory(String category) {
        if (category == null) {
            return;
        }

        try {
            // Scan asset directory for category
            String categoryPath = assetBasePath + category + "/";
            File categoryDir = new File(categoryPath);

            if (!categoryDir.exists() || !categoryDir.isDirectory()) {
                System.err.println("Warning: Category directory not found: " + categoryPath);
                return;
            }

            // Get list of all files in category directory
            File[] files = categoryDir.listFiles();
            if (files == null) {
                System.err.println("Warning: Could not list files in directory: " + categoryPath);
                return;
            }

            List<String> assetList = new ArrayList<>();
            for (File file : files) {
                if (file.isFile()) {
                    assetList.add(file.getName());
                }
            }

            // Call preloadAssets() with file list
            System.out.println("Preloading " + assetList.size() + " assets from category: " + category);
            preloadAssets(assetList);

        } catch (Exception e) {
            // Handle directory scanning errors
            System.err.println("Error preloading category " + category + ": " + e.getMessage());
        }
    }

    /**
     * Unloads an asset from cache to free memory.
     *
     * @param assetName The name of the asset to unload
     * @param category  The category of the asset
     * @return true if asset was unloaded, false if not found
     */
    public boolean unloadAsset(String assetName, String category) {
        if (assetName == null || category == null) {
            return false;
        }

        // Switch on category to select appropriate cache
        switch (category.toLowerCase()) {
            case SPRITE_CATEGORY:
                if (spriteCache.containsKey(assetName)) {
                    spriteCache.remove(assetName);
                    return true;
                }
                break;
            case AUDIO_CATEGORY:
                if (audioCache.containsKey(assetName)) {
                    audioCache.remove(assetName);
                    return true;
                }
                break;
            case THEME_CATEGORY:
                if (themeCache.containsKey(assetName)) {
                    themeCache.remove(assetName);
                    return true;
                }
                break;
            default:
                return false;
        }

        // Return success status
        return false;
    }

    /**
     * Clears all cached assets to free memory.
     * Use with caution as this will require reloading assets.
     */
    public void clearCache() {
        // Log cache clearing action
        int totalCachedAssets = spriteCache.size() + audioCache.size() + themeCache.size() + imageCache.size();
        System.out.println("Clearing asset cache: " + totalCachedAssets + " assets will be removed");

        // Clear all cache maps
        spriteCache.clear();
        audioCache.clear();
        themeCache.clear();
        imageCache.clear();

        // Reset statistics counters
        totalAssetsLoaded = 0;
        cacheHits = 0;
        cacheMisses = 0;
        totalLoadTime = 0;

        // Optionally trigger garbage collection
        System.gc();

        System.out.println("Asset cache cleared successfully");
    }

    /**
     * Gets the default sprite for fallback when loading fails.
     *
     * @return A safe fallback sprite
     */
    public TETile getDefaultSprite() {
        // Return a safe fallback sprite (Tileset.NOTHING)
        // This sprite should never fail to display
        return Tileset.NOTHING;
    }

    /**
     * Checks if an asset exists in the file system.
     *
     * @param assetName The asset name to check
     * @param category  The asset category
     * @return true if asset file exists
     */
    public boolean assetExists(String assetName, String category) {
        if (assetName == null || category == null) {
            return false;
        }

        // Construct full file path
        String fullPath = assetBasePath + category + "/" + assetName;

        // Check if file exists using File.exists()
        File assetFile = new File(fullPath);
        if (assetFile.exists()) {
            return true;
        }

        // Handle different file extensions for same asset
        // Try common extensions if the exact file doesn't exist
        String baseName = assetName.replaceAll("\\.[^.]*$", "");
        String[] commonExtensions = {"png", "jpg", "jpeg", "gif", "bmp", "wav", "mp3", "ogg", "json"};

        for (String ext : commonExtensions) {
            File altFile = new File(assetBasePath + category + "/" + baseName + "." + ext);
            if (altFile.exists()) {
                return true;
            }
        }

        // Return existence status
        return false;
    }

    /**
     * Gets asset loading statistics for performance monitoring.
     *
     * @return Map containing loading statistics
     */
    public Map<String, Object> getLoadingStats() {
        Map<String, Object> stats = new HashMap<>();

        // Create map with statistics:
        stats.put("totalAssetsLoaded", totalAssetsLoaded);
        stats.put("cacheHits", cacheHits);
        stats.put("cacheMisses", cacheMisses);

        // hit ratio (cacheHits / (cacheHits + cacheMisses))
        int totalRequests = cacheHits + cacheMisses;
        double hitRatio = totalRequests > 0 ? (double) cacheHits / totalRequests : 0.0;
        stats.put("hitRatio", hitRatio);

        // totalLoadTime, averageLoadTime
        stats.put("totalLoadTime", totalLoadTime);
        double averageLoadTime = totalAssetsLoaded > 0 ? (double) totalLoadTime / totalAssetsLoaded : 0.0;
        stats.put("averageLoadTime", averageLoadTime);

        // cache sizes for each category
        stats.put("spriteCacheSize", spriteCache.size());
        stats.put("audioCacheSize", audioCache.size());
        stats.put("themeCacheSize", themeCache.size());
        stats.put("imageCacheSize", imageCache.size());

        // Additional useful statistics
        stats.put("maxCacheSize", maxCacheSize);
        stats.put("cachingEnabled", enableCaching);
        stats.put("assetBasePath", assetBasePath);

        // Return statistics map
        return stats;
    }

    /**
     * Optimizes cache by removing least recently used assets.
     * Called automatically when cache size exceeds limits.
     */
    private void optimizeCache() {
        // Simple cache optimization: remove random entries when cache is full
        // In a more sophisticated implementation, this would use LRU tracking

        int totalCacheSize = spriteCache.size() + audioCache.size() + themeCache.size() + imageCache.size();

        if (totalCacheSize < maxCacheSize) {
            return; // No optimization needed
        }

        int targetRemovalCount = Math.max(1, totalCacheSize - (maxCacheSize * 3 / 4)); // Remove 25% when full
        int removedCount = 0;

        // Remove from sprite cache first (usually largest)
        if (removedCount < targetRemovalCount && !spriteCache.isEmpty()) {
            String[] keys = spriteCache.keySet().toArray(new String[0]);
            for (int i = 0; i < Math.min(targetRemovalCount - removedCount, keys.length / 2); i++) {
                spriteCache.remove(keys[i]);
                removedCount++;
            }
        }

        // Remove from audio cache if needed
        if (removedCount < targetRemovalCount && !audioCache.isEmpty()) {
            String[] keys = audioCache.keySet().toArray(new String[0]);
            for (int i = 0; i < Math.min(targetRemovalCount - removedCount, keys.length / 2); i++) {
                audioCache.remove(keys[i]);
                removedCount++;
            }
        }

        // Remove from image cache if needed
        if (removedCount < targetRemovalCount && !imageCache.isEmpty()) {
            String[] keys = imageCache.keySet().toArray(new String[0]);
            for (int i = 0; i < Math.min(targetRemovalCount - removedCount, keys.length / 2); i++) {
                imageCache.remove(keys[i]);
                removedCount++;
            }
        }

        System.out.println("Cache optimized: removed " + removedCount + " assets");
    }

    /**
     * Validates asset file integrity.
     *
     * @param assetPath The path to the asset file
     * @return true if asset is valid and loadable
     */
    private boolean validateAsset(String assetPath) {
        if (assetPath == null) {
            return false;
        }

        File assetFile = new File(assetPath);

        // Check file exists and is readable
        if (!assetFile.exists() || !assetFile.canRead()) {
            return false;
        }

        // Check file size is reasonable (not empty, not too large)
        long fileSize = assetFile.length();
        if (fileSize == 0 || fileSize > 100 * 1024 * 1024) { // Max 100MB
            return false;
        }

        // Get file extension for format validation
        String extension = getFileExtension(assetPath).toLowerCase();

        try {
            // For images: verify format and dimensions
            if (extension.matches("png|jpg|jpeg|gif|bmp")) {
                BufferedImage image = ImageIO.read(assetFile);
                if (image == null) {
                    return false;
                }
                // Check reasonable dimensions (not 0x0, not too large)
                int width = image.getWidth();
                int height = image.getHeight();
                return width > 0 && height > 0 && width <= 4096 && height <= 4096;
            }

            // For audio: basic file existence check (detailed validation would require audio library)
            if (extension.matches("wav|mp3|ogg|aiff")) {
                return true; // Basic validation passed
            }

            // For other files (themes, etc.): basic validation
            return true;

        } catch (Exception e) {
            // Return validation result
            return false;
        }
    }

    // Configuration getters and setters
    public boolean isCachingEnabled() {
        return enableCaching;
    }

    public void setCachingEnabled(boolean enabled) {
        this.enableCaching = enabled;
    }

    public int getMaxCacheSize() {
        return maxCacheSize;
    }

    public void setMaxCacheSize(int size) {
        this.maxCacheSize = Math.max(1, size);
    }

    public String getAssetBasePath() {
        return assetBasePath;
    }

    public void setAssetBasePath(String path) {
        this.assetBasePath = path;
    }

    // Statistics getters
    public int getTotalAssetsLoaded() {
        return totalAssetsLoaded;
    }

    public int getCacheHits() {
        return cacheHits;
    }

    public int getCacheMisses() {
        return cacheMisses;
    }

    public double getCacheHitRatio() {
        int total = cacheHits + cacheMisses;
        return total > 0 ? (double) cacheHits / total : 0.0;
    }

    public long getTotalLoadTime() {
        return totalLoadTime;
    }

    public double getAverageLoadTime() {
        return totalAssetsLoaded > 0 ? (double) totalLoadTime / totalAssetsLoaded : 0.0;
    }
}