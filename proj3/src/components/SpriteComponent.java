package components;

import tileengine.TETile;
import components.MovementComponent.Direction;

/**
 * Sprite management component with directional animations and frame cycling.
 * Handles sprite selection, animation timing, and directional sprite changes.
 * <p>
 * This component provides:
 * - Multiple animation frames for smooth movement
 * - Directional sprite variants (up, down, left, right)
 * - Animation timing and frame cycling
 * - Sprite state management
 * - Integration with movement direction
 */
public class SpriteComponent {
    // Animation frames for different directions
    private TETile[] upFrames;
    private TETile[] downFrames;
    private TETile[] leftFrames;
    private TETile[] rightFrames;
    private TETile[] idleFrames;

    // Current animation state
    private TETile[] currentFrames;
    private int currentFrame;
    private Direction currentDirection;

    // Animation timing
    private double frameTime; // Time per frame in seconds
    private double timeSinceLastFrame;
    private boolean isAnimating;

    // Default/fallback sprite
    private TETile defaultSprite;

    /**
     * Creates a SpriteComponent with a single default sprite.
     * Use this for static entities or entities with simple sprites.
     *
     * @param defaultSprite The default sprite to display
     */
    public SpriteComponent(TETile defaultSprite) {
        // - Set this.defaultSprite = defaultSprite
        this.defaultSprite = defaultSprite;
        // - Initialize all frame arrays as single-element arrays with defaultSprite
        this.upFrames = new TETile[]{defaultSprite};
        this.downFrames = new TETile[]{defaultSprite};
        this.leftFrames = new TETile[]{defaultSprite};
        this.rightFrames = new TETile[]{defaultSprite};
        this.idleFrames = new TETile[]{defaultSprite};
        // - Set currentFrames to defaultSprite array
        this.currentFrames = this.idleFrames;
        // - Set currentFrame to 0
        this.currentFrame = 0;
        // - Set currentDirection to NONE
        this.currentDirection = Direction.NONE;
        // - Set frameTime to reasonable default (e.g., 0.2 seconds)
        this.frameTime = 0.2; // TODO: consider moving to GameConfig
        // - Set isAnimating to false
        this.isAnimating = false;
    }

    /**
     * Creates a SpriteComponent with animation frames.
     *
     * @param frames    Array of animation frames to cycle through
     * @param frameTime Time between frames in seconds
     */
    public SpriteComponent(TETile[] frames, double frameTime) {
        // - Set all directional frame arrays to copies of frames
        this.upFrames = frames.clone();
        this.downFrames = frames.clone();
        this.leftFrames = frames.clone();
        this.rightFrames = frames.clone();
        this.idleFrames = frames.clone();
        // - Set this.frameTime = frameTime
        this.frameTime = frameTime;
        // - Set currentFrames to frames
        this.currentFrames = frames;
        // - Set currentFrame to 0
        this.currentFrame = 0;
        // - Set currentDirection to NONE
        this.currentDirection = Direction.NONE;
        // - Set isAnimating to true if frames.length > 1
        this.isAnimating = frames.length > 1;
        // - Set defaultSprite to frames[0]
        this.defaultSprite = frames[0];
    }

    /**
     * Creates a SpriteComponent with directional animations.
     *
     * @param upFrames    Animation frames for upward movement
     * @param downFrames  Animation frames for downward movement
     * @param leftFrames  Animation frames for leftward movement
     * @param rightFrames Animation frames for rightward movement
     * @param idleFrames  Animation frames for idle state
     * @param frameTime   Time between frames in seconds
     */
    public SpriteComponent(TETile[] upFrames, TETile[] downFrames,
                           TETile[] leftFrames, TETile[] rightFrames,
                           TETile[] idleFrames, double frameTime) {
        // Set all frame arrays to provided arrays (copy to prevent external modification)
        this.upFrames = upFrames != null ? upFrames.clone() : new TETile[0];
        this.downFrames = downFrames != null ? downFrames.clone() : new TETile[0];
        this.leftFrames = leftFrames != null ? leftFrames.clone() : new TETile[0];
        this.rightFrames = rightFrames != null ? rightFrames.clone() : new TETile[0];
        this.idleFrames = idleFrames != null ? idleFrames.clone() : new TETile[0];

        // Set this.frameTime = frameTime
        this.frameTime = frameTime;

        // Set currentFrames to idleFrames initially
        this.currentFrames = this.idleFrames;

        // Set currentFrame to 0
        this.currentFrame = 0;

        // Set currentDirection to NONE
        this.currentDirection = Direction.NONE;

        // Set isAnimating based on frame array lengths
        this.isAnimating = this.idleFrames.length > 1;

        // Set defaultSprite to idleFrames[0] or first available frame
        this.defaultSprite = this.idleFrames.length > 0 ? this.idleFrames[0] : null;
        if (this.defaultSprite == null && this.upFrames.length > 0) {
            this.defaultSprite = this.upFrames[0];
        }
    }

    /**
     * Updates the sprite animation based on elapsed time.
     * Should be called every frame during game updates.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    public void updateAnimation(double deltaTime) {
        // If not isAnimating or currentFrames is null/empty, return early
        if (!isAnimating || currentFrames == null || currentFrames.length <= 1) {
            return;
        }

        // Add deltaTime to timeSinceLastFrame
        this.timeSinceLastFrame += deltaTime;

        // If timeSinceLastFrame >= frameTime:
        if (this.timeSinceLastFrame >= frameTime) {
            // Advance currentFrame to next frame (wrap around at end)
            this.currentFrame = (this.currentFrame + 1) % currentFrames.length;

            // Reset timeSinceLastFrame (subtract frameTime to maintain timing)
            this.timeSinceLastFrame -= frameTime;
        }

        // Handle edge cases like paused animation or single-frame sprites
        if (this.timeSinceLastFrame < 0) {
            this.timeSinceLastFrame = 0;
        }
    }

    /**
     * Sets the sprite direction based on movement direction.
     * Automatically selects the appropriate sprite frames for the direction.
     *
     * @param direction The movement direction to match sprite to
     */
    public void setDirection(Direction direction) {
        // If direction equals currentDirection, return early (no change)
        if (direction == this.currentDirection) {
            return;
        }

        // Set currentDirection = direction
        this.currentDirection = direction;

        // Switch on direction:
        switch (direction) {
            case UP:
                // UP: set currentFrames = upFrames
                this.currentFrames = upFrames;
                break;
            case DOWN:
                // DOWN: set currentFrames = downFrames
                this.currentFrames = downFrames;
                break;
            case LEFT:
                // LEFT: set currentFrames = leftFrames
                this.currentFrames = leftFrames;
                break;
            case RIGHT:
                // RIGHT: set currentFrames = rightFrames
                this.currentFrames = rightFrames;
                break;
            case NONE:
            default:
                // NONE: set currentFrames = idleFrames
                this.currentFrames = idleFrames;
                break;
        }

        // Reset currentFrame to 0 when changing directions
        this.currentFrame = 0;

        // Reset animation timing for smooth transition
        this.timeSinceLastFrame = 0.0;
    }

    /**
     * Gets the current sprite frame to be rendered.
     * This is the main method used by the rendering system.
     *
     * @return The TETile representing the current animation frame
     */
    public TETile getCurrentFrame() {
        // If currentFrames is null or empty, return defaultSprite
        if (currentFrames == null || currentFrames.length == 0) {
            return defaultSprite;
        }

        // Ensure currentFrame index is valid (clamp to array bounds)
        if (currentFrame < 0) {
            currentFrame = 0;
        } else if (currentFrame >= currentFrames.length) {
            currentFrame = currentFrames.length - 1;
        }

        // Return currentFrames[currentFrame]
        return currentFrames[currentFrame];
    }

    /**
     * Sets a specific animation frame manually.
     * Useful for specific states or debugging.
     *
     * @param frameIndex The frame index to set (will be clamped to valid range)
     */
    public void setFrame(int frameIndex) {
        // Validate frameIndex is within bounds of currentFrames array
        if (currentFrames == null || currentFrames.length == 0) {
            return;
        }

        // Clamp frameIndex to valid range [0, currentFrames.length - 1]
        if (frameIndex < 0) {
            frameIndex = 0;
        } else if (frameIndex >= currentFrames.length) {
            frameIndex = currentFrames.length - 1;
        }

        // Set currentFrame = frameIndex
        this.currentFrame = frameIndex;

        // Reset timeSinceLastFrame to sync timing
        this.timeSinceLastFrame = 0.0;
    }

    /**
     * Pauses or resumes sprite animation.
     *
     * @param animate true to enable animation, false to pause
     */
    public void setAnimating(boolean animate) {
        // Set isAnimating = animate
        this.isAnimating = animate;

        // If pausing, keep current frame
        // If resuming, reset timing for smooth restart
        if (animate) {
            this.timeSinceLastFrame = 0.0;
        }
    }

    /**
     * Changes the animation speed by setting frame time.
     *
     * @param frameTime New time between frames in seconds
     */
    public void setFrameTime(double frameTime) {
        // Validate frameTime > 0
        if (frameTime <= 0) {
            throw new IllegalArgumentException("Frame time must be positive");
        }

        // Adjust timeSinceLastFrame proportionally to maintain smooth animation
        if (this.frameTime > 0) {
            double ratio = frameTime / this.frameTime;
            this.timeSinceLastFrame *= ratio;
        }

        // Set this.frameTime = frameTime
        this.frameTime = frameTime;
    }

    /**
     * Replaces the sprite frames for a specific direction.
     * Allows dynamic sprite changing during gameplay.
     *
     * @param direction The direction to update frames for
     * @param frames    The new animation frames
     */
    public void setDirectionalFrames(Direction direction, TETile[] frames) {
        // Validate frames array is not null and not empty
        if (frames == null || frames.length == 0) {
            throw new IllegalArgumentException("Frames array cannot be null or empty");
        }

        // Copy frames array to prevent external modification
        TETile[] framesCopy = frames.clone();

        // Switch on direction and update appropriate frame array
        switch (direction) {
            case UP:
                this.upFrames = framesCopy;
                break;
            case DOWN:
                this.downFrames = framesCopy;
                break;
            case LEFT:
                this.leftFrames = framesCopy;
                break;
            case RIGHT:
                this.rightFrames = framesCopy;
                break;
            case NONE:
                this.idleFrames = framesCopy;
                break;
        }

        // If direction matches currentDirection, update currentFrames and reset frame
        if (direction == this.currentDirection) {
            this.currentFrames = framesCopy;
            this.currentFrame = 0;
            this.timeSinceLastFrame = 0.0;
        }
    }

    /**
     * Resets the animation to the first frame.
     * Useful for state changes or synchronization.
     */
    public void resetAnimation() {
        // Set currentFrame = 0
        this.currentFrame = 0;

        // Set timeSinceLastFrame = 0
        this.timeSinceLastFrame = 0.0;

        // Keep current direction and frames
    }

    /**
     * Gets the total number of frames in the current animation.
     *
     * @return The number of frames in the current direction's animation
     */
    public int getFrameCount() {
        // Return currentFrames.length if currentFrames is not null
        if (currentFrames != null) {
            return currentFrames.length;
        }

        // Return 1 if currentFrames is null (default sprite)
        return 1;
    }

    /**
     * Checks if the sprite is currently animating.
     *
     * @return true if animation is active, false if paused or static
     */
    public boolean isAnimating() {
        return isAnimating && getFrameCount() > 1;
    }

    /**
     * Gets the current animation progress as a percentage.
     *
     * @return Animation progress from 0.0 to 1.0
     */
    public double getAnimationProgress() {
        // If not animating or single frame, return 0.0
        if (!isAnimating || getFrameCount() <= 1) {
            return 0.0;
        }

        // Calculate progress within current frame: timeSinceLastFrame / frameTime
        double frameProgress = frameTime > 0 ? timeSinceLastFrame / frameTime : 0.0;

        // Calculate overall progress: (currentFrame + frameProgress) / totalFrames
        double totalFrames = getFrameCount();
        double overallProgress = (currentFrame + frameProgress) / totalFrames;

        // Return value between 0.0 and 1.0
        return Math.max(0.0, Math.min(1.0, overallProgress));
    }

    /**
     * Creates a copy of this SpriteComponent.
     * Useful for creating multiple entities with similar sprites.
     *
     * @return A new SpriteComponent with the same configuration
     */
    public SpriteComponent copy() {
        // Create new SpriteComponent with same directional frames
        SpriteComponent copy = new SpriteComponent(
                this.upFrames.clone(),
                this.downFrames.clone(),
                this.leftFrames.clone(),
                this.rightFrames.clone(),
                this.idleFrames.clone(),
                this.frameTime
        );

        // Copy animation timing settings
        copy.timeSinceLastFrame = this.timeSinceLastFrame;
        copy.isAnimating = this.isAnimating;

        // Copy current state (direction, frame, etc.)
        copy.currentDirection = this.currentDirection;
        copy.currentFrame = this.currentFrame;
        copy.setDirection(this.currentDirection); // This will set currentFrames correctly

        // Return independent copy
        return copy;
    }

    // Getters
    public Direction getCurrentDirection() {
        return currentDirection;
    }

    public int getCurrentFrameIndex() {
        return currentFrame;
    }

    public double getFrameTime() {
        return frameTime;
    }

    public TETile getDefaultSprite() {
        return defaultSprite;
    }
}