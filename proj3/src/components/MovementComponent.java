package components;

import tileengine.TETile;
import tileengine.Tileset;

/**
 * Smooth movement component with velocity and interpolation support.
 * Handles entity movement, collision detection, and smooth animation.
 * <p>
 * This component provides:
 * - Velocity-based movement with configurable speeds
 * - Smooth interpolation between tile positions
 * - Collision detection with world tiles
 * - Direction tracking for sprite animation
 * - Movement state management
 */
public class MovementComponent {
    // Current velocity
    private double velocityX;
    private double velocityY;

    // Movement constraints
    private double maxSpeed;
    private boolean canMove;

    // Direction tracking
    private Direction currentDirection;
    private Direction pendingDirection;

    // Movement state
    private boolean isMoving;
    private double movementProgress; // 0.0 to 1.0 for tile transition

    // Reference to entity's position component
    private Position position;

    // World reference for collision detection
    private TETile[][] world;

    /**
     * Enumeration of movement directions.
     * Used for both movement logic and sprite direction.
     */
    public enum Direction {
        NONE(0, 0),
        UP(0, 1),
        DOWN(0, -1),
        LEFT(-1, 0),
        RIGHT(1, 0);

        public final int deltaX;
        public final int deltaY;

        Direction(int deltaX, int deltaY) {
            this.deltaX = deltaX;
            this.deltaY = deltaY;
        }

        /**
         * Gets the opposite direction.
         * Used for collision response and pathfinding.
         */
        public Direction getOpposite() {
            if (this == UP) {
                return DOWN;
            } else if (this == DOWN) {
                return UP;
            } else if (this == LEFT) {
                return RIGHT;
            } else if (this == RIGHT) {
                return LEFT;
            } else {
                return NONE;
            }
        }
    }

    /**
     * Creates a MovementComponent with specified maximum speed.
     *
     * @param position The position component this movement controls
     * @param maxSpeed Maximum movement speed in tiles per second
     */
    public MovementComponent(Position position, double maxSpeed) {
        // - Store position reference
        this.position = position;
        // - Set maxSpeed
        this.maxSpeed = maxSpeed;
        // - Initialize velocities to 0
        this.velocityX = 0;
        // - Set currentDirection and pendingDirection to NONE
        this.currentDirection = Direction.NONE;
        this.pendingDirection = Direction.NONE;
        // - Set isMoving to false
        this.isMoving = false;
        // - Set canMove to true
        this.canMove = true;
        // - Initialize movementProgress to 0.0
        this.movementProgress = 0.0;
    }

    /**
     * Sets the world reference for collision detection.
     * Must be called before movement can occur.
     *
     * @param world The 2D world array for collision checking
     */
    public void setWorld(TETile[][] world) {
        // - Validate world is not null
        if (world == null) {
            throw new IllegalArgumentException("World cannot be null");
        }
        // - Store world reference for collision detection
        this.world = world;
    }

    /**
     * Sets the desired movement velocity.
     * Velocity will be clamped to maximum speed.
     *
     * @param vx Desired velocity in X direction (tiles per second)
     * @param vy Desired velocity in Y direction (tiles per second)
     */
    public void setVelocity(double vx, double vy) {
        // - Calculate velocity magnitude: Math.hypot(vx, vy)
        double magnitude = Math.hypot(vx, vy);
        // - If magnitude > maxSpeed, normalize and scale to maxSpeed
        if (magnitude > maxSpeed) {
            vx = vx / magnitude * maxSpeed;
            vy = vy / magnitude * maxSpeed;
        }
        // - Set velocityX and velocityY
        this.velocityX = vx;
        this.velocityY = vy;
        // - Update currentDirection based on primary velocity direction
        if (Math.abs(vx) > Math.abs(vy)) {
            this.currentDirection = vx > 0 ? Direction.RIGHT : Direction.LEFT;
        } else {
            this.currentDirection = vy > 0 ? Direction.UP : Direction.DOWN;
        }
        // - Update isMoving based on whether velocity is non-zero
        this.isMoving = magnitude > 0;
    }

    /**
     * Sets movement direction and speed.
     * Convenience method for directional movement.
     *
     * @param direction The direction to move in
     * @param speed     The speed to move at (will be clamped to maxSpeed)
     */
    public void setDirection(Direction direction, double speed) {
        // - Clamp speed to maxSpeed
        speed = Math.min(speed, maxSpeed);
        // - Calculate velocityX = direction.deltaX * speed
        this.velocityX = direction.deltaX * speed;
        // - Calculate velocityY = direction.deltaY * speed
        this.velocityY = direction.deltaY * speed;
        // - Update currentDirection
        this.currentDirection = direction;
        // - Update isMoving based on direction != NONE
        this.isMoving = direction != Direction.NONE;
    }

    /**
     * Attempts to change direction if the new direction is valid.
     * Used for responsive controls that don't interrupt current movement.
     *
     * @param newDirection The desired new direction
     * @return true if direction change was successful, false if blocked
     */
    public boolean tryChangeDirection(Direction newDirection) {
        // - Check if movement in newDirection is possible using canMoveInDirection()
        if (!canMoveInDirection(newDirection)) {
            return false;
        }
        // - If possible, set pendingDirection = newDirection
        this.pendingDirection = newDirection;
        // - If currently not moving or direction change is immediate, apply change
        if (!isMoving || newDirection == currentDirection.getOpposite()) {
            setDirection(newDirection, maxSpeed);
        }
        // - Update velocity based on new direction
        setDirection(newDirection, maxSpeed);
        // - Return success status
        return true;
    }

    /**
     * Updates position based on current velocity and delta time.
     * Handles collision detection and smooth movement.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    public void updatePosition(double deltaTime) {
        // TODO: Update position with collision detection
        // - If not canMove or velocities are zero, return early
        if (!canMove || (velocityX == 0 && velocityY == 0)) {
            return;
        }
        // - Calculate new position: newX = position.x + velocityX * deltaTime
        double newX = position.getX() + velocityX * deltaTime;
        double newY = position.getY() + velocityY * deltaTime;
        // - Check collision at new position using isPositionValid()
        if (!isPositionValid(newX, newY)) {
            // - Stop movement
            stop();
            // - Try to apply pendingDirection if it exists
            if (pendingDirection != Direction.NONE) {
                setDirection(pendingDirection, maxSpeed);
            }
            return;
        } else {
            // - Update position using position.setPosition()
            position.setPosition(newX, newY);
            // - Update movementProgress for tile transitions
            movementProgress += Math.hypot(velocityX, velocityY) * deltaTime;
            // - Check if entered new tile and handle accordingly
            if (movementProgress >= 1.0) {
                movementProgress -= 1.0;
                // Handle tile entry logic here
            }
        }
    }

    /**
     * Checks if movement is possible in the specified direction.
     * Used for pre-movement validation and AI pathfinding.
     *
     * @param direction The direction to check
     * @return true if movement is possible, false if blocked
     */
    public boolean canMoveInDirection(Direction direction) {
        // - Calculate target position: currentTile + direction delta
        int targetX = position.tileX() + direction.deltaX;
        int targetY = position.tileY() + direction.deltaY;
        // - Check world bounds
        if (targetX < 0 || targetX >= world.length || targetY < 0 || targetY >= world[0].length) {
            return false;
        }
        // - Check if target tile is walkable (not WALL)
        if (world[targetX][targetY] == Tileset.WALL) {
            return false;
        }
        // - Return true if valid, false otherwise
        return true;
    }

    /**
     * Checks if a specific position is valid (not colliding with walls).
     *
     * @param x The x-coordinate to check
     * @param y The y-coordinate to check
     * @return true if position is valid, false if collision
     */
    private boolean isPositionValid(double x, double y) {
        // TODO: Consider entity size/collision box if needed
        // - Convert x,y to tile coordinates
        int tileX = (int) Math.floor(x);
        int tileY = (int) Math.floor(y);
        // - Check world bounds
        if (tileX < 0 || tileX >= world.length || tileY < 0 || tileY >= world[0].length) {
            return false;
        }
        // - Check if tile at position is walkable
        if (world[tileX][tileY] == Tileset.WALL) {
            return false;
        }
        return true;
    }

    /**
     * Stops all movement immediately.
     * Used for collision response and forced stops.
     */
    public void stop() {
        // - Set velocityX and velocityY to 0
        this.velocityX = 0;
        this.velocityY = 0;
        // - Set isMoving to false
        this.isMoving = false;
        // - Set currentDirection to NONE
        this.currentDirection = Direction.NONE;
        // - Clear pendingDirection
        this.pendingDirection = Direction.NONE;
        // - Reset movementProgress
        this.movementProgress = 0.0;
    }

    /**
     * Pauses movement without losing velocity information.
     * Used when game is paused or entity is temporarily disabled.
     */
    public void pause() {
        // - Set canMove to false
        // - Keep velocity information for resume
        this.canMove = false;
    }

    /**
     * Resumes movement after pause.
     * Restores movement capability without changing velocity.
     */
    public void resume() {
        // - Set canMove to true
        // - Movement will continue with previous velocity on next update
        this.canMove = true;
    }

    /**
     * Gets the movement progress within the current tile transition.
     * Used for smooth sprite animation and interpolation.
     *
     * @return Progress from 0.0 (start of tile) to 1.0 (end of tile)
     */
    public double getMovementProgress() {
        // - Based on fractional position within current tile
        // - Return value between 0.0 and 1.0
        // - Used for animation timing and interpolation
        return movementProgress;
    }

    /**
     * Checks if the entity is currently in motion.
     *
     * @return true if moving, false if stationary
     */
    public boolean isMoving() {
        return isMoving;
    }

    /**
     * Gets the current movement direction.
     *
     * @return The current direction of movement
     */
    public Direction getCurrentDirection() {
        return currentDirection;
    }

    /**
     * Gets the pending direction change.
     *
     * @return The direction queued for next valid opportunity, or NONE
     */
    public Direction getPendingDirection() {
        return pendingDirection;
    }

    /**
     * Gets the current velocity magnitude.
     *
     * @return The speed of movement in tiles per second
     */
    public double getSpeed() {
        return Math.hypot(velocityX, velocityY);
    }

    /**
     * Gets the maximum allowed speed.
     *
     * @return The maximum speed in tiles per second
     */
    public double getMaxSpeed() {
        return maxSpeed;
    }

    /**
     * Sets a new maximum speed.
     * Current velocity will be adjusted if it exceeds the new limit.
     *
     * @param maxSpeed The new maximum speed
     */
    public void setMaxSpeed(double maxSpeed) {
        // - Set this.maxSpeed = maxSpeed
        this.maxSpeed = maxSpeed;
        // - If current velocity exceeds new limit, scale it down
        // - Maintain direction while adjusting magnitude
        if (getSpeed() > maxSpeed) {
            setVelocity(velocityX, velocityY);
        }
    }

    // Getters for velocity components
    public double getVelocityX() {
        return velocityX;
    }

    public double getVelocityY() {
        return velocityY;
    }

    public boolean canMove() {
        return canMove;
    }
}