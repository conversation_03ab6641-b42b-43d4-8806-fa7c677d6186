package components;

import core.Pos;

/**
 * Enhanced position component with sub-tile precision for smooth movement.
 * Extends the basic Pos class to support floating-point coordinates while
 * maintaining compatibility with the existing tile-based world system.
 * <p>
 * This class is essential for:
 * - Smooth entity movement between tiles
 * - Interpolated animation rendering
 * - Precise collision detection
 * - Camera positioning and scrolling
 */
public class Position {
    // Precise floating-point coordinates
    private double x;
    private double y;

    // Previous position for interpolation
    private double previousX;
    private double previousY;

    // Movement tracking
    private boolean hasMoved;
    private long lastMoveTime;

    /**
     * Creates a new Position at the specified coordinates.
     *
     * @param x The x-coordinate (can be fractional for sub-tile positioning)
     * @param y The y-coordinate (can be fractional for sub-tile positioning)
     */
    public Position(double x, double y) {
        // - Set this.x and this.y to provided values
        this.x = x;
        this.y = y;
        // - Set previousX and previousY to same values (no movement initially)
        this.previousX = x;
        this.previousY = y;
        // - Set hasMoved to false
        this.hasMoved = false;
        // - Set lastMoveTime to current system time
        this.lastMoveTime = System.currentTimeMillis();
    }

    /**
     * Creates a Position from an existing tile-based Pos object.
     * Converts integer tile coordinates to floating-point coordinates.
     *
     * @param pos The tile-based position to convert
     */
    public Position(Pos pos) {
        // - Use pos.x and pos.y as starting coordinates
        // - Call the main constructor with converted values
        this(pos.x, pos.y);
    }

    /**
     * Updates the position, storing the previous location for interpolation.
     * This method should be called when an entity moves.
     * This method would update @hasMoved, @lastMoveTime if position changed.
     *
     * @param newX The new x-coordinate
     * @param newY The new y-coordinate
     */
    public void setPosition(double newX, double newY) {
        // - Store current x,y as previousX,previousY
        this.previousX = this.x;
        this.previousY = this.y;
        // - Set x,y to newX,newY
        this.x = newX;
        this.y = newY;
        if (this.x != this.previousX || this.y != this.previousY) {
            // - Set hasMoved based on whether position actually changed
            this.hasMoved = true;
            // - Update lastMoveTime to current system time if moved
            this.lastMoveTime = System.currentTimeMillis();
        }
    }

    /**
     * Moves the position by the specified offset.
     * Equivalent to setPosition(x + deltaX, y + deltaY).
     *
     * @param deltaX The amount to move in the x direction
     * @param deltaY The amount to move in the y direction
     */
    public void move(double deltaX, double deltaY) {
        // - Call setPosition with current position plus deltas
        // - Ensure movement tracking is maintained
        setPosition(this.x + deltaX, this.y + deltaY);
    }

    /**
     * Gets the current tile X coordinate (integer).
     * Used for tile-based collision detection and world interaction.
     *
     * @return The X coordinate rounded down to the nearest integer
     */
    public int tileX() {
        return (int) Math.floor(x);
    }

    /**
     * Gets the current tile Y coordinate (integer).
     * Used for tile-based collision detection and world interaction.
     *
     * @return The Y coordinate rounded down to the nearest integer
     */
    public int tileY() {
        return (int) Math.floor(y);
    }

    /**
     * Gets the fractional part of the X coordinate within the tile.
     * Returns a value between 0.0 and 1.0 representing position within the tile.
     *
     * @return The fractional X position within the current tile
     */
    public double getFractionalX() {
        return x - Math.floor(x);
    }

    /**
     * Gets the fractional part of the Y coordinate within the tile.
     * Returns a value between 0.0 and 1.0 representing position within the tile.
     *
     * @return The fractional Y position within the current tile
     */
    public double getFractionalY() {
        return y - Math.floor(y);
    }

    /**
     * Calculates the Euclidean distance to another position.
     * Used for proximity checks, pathfinding, and range calculations.
     *
     * @param other The other position to measure distance to
     * @return The distance between this position and the other position
     */
    public double distanceTo(Position other) {
        if (other == null) {
            throw new IllegalArgumentException("Other position cannot be null");
        }
        return Math.hypot(x - other.x, y - other.y);
    }

    /**
     * Calculates the squared distance to another position.
     * More efficient than distanceTo() when only comparing distances.
     *
     * @param other The other position to measure distance to
     * @return The squared distance between positions
     */
    public double distanceSquaredTo(Position other) {
        if (other == null) {
            throw new IllegalArgumentException("Other position cannot be null");
        }
        return Math.pow(x - other.x, 2) + Math.pow(y - other.y, 2);
    }

    /**
     * Gets an interpolated position between previous and current position.
     * Used for smooth rendering when frame rate doesn't match update rate.
     *
     * @param alpha Interpolation factor (0.0 = previous position, 1.0 = current position)
     * @return A new Position representing the interpolated location
     */
    public Position getInterpolated(double alpha) {
        if (alpha < 0.0) {
            alpha = 0.0;
        } else if (alpha > 1.0) {
            alpha = 1.0;
        }
        double interpolatedX = previousX + alpha * (x - previousX);
        double interpolatedY = previousY + alpha * (y - previousY);
        return new Position(interpolatedX, interpolatedY);
    }

    /**
     * Converts this Position to a tile-based Pos object.
     * Uses integer tile coordinates, losing sub-tile precision.
     *
     * @return A new Pos object representing the current tile position
     */
    public Pos toPos() {
        return new Pos(tileX(), tileY());
    }

    /**
     * Checks if the position has changed since the last update.
     * Used to optimize rendering and update logic.
     *
     * @return true if the position has moved, false otherwise
     */
    public boolean hasMovedRecently() {
        // TODO: Could also check if lastMoveTime is within a certain threshold
        return hasMoved;
    }

    /**
     * Resets the movement tracking flags.
     * Should be called after processing movement in render/update cycles.
     */
    public void clearMovementFlag() {
        // TODO: Keep lastMoveTime for potential time-based checks
        this.hasMoved = false;
    }

    // Getters and setters
    public double getX() {
        return x;
    }

    public double getY() {
        return y;
    }

    public double getPreviousX() {
        return previousX;
    }

    public double getPreviousY() {
        return previousY;
    }

    public long getLastMoveTime() {
        return lastMoveTime;
    }

    @Override
    public String toString() {
        return String.format("Position(%.2f, %.2f) [Tile: %d, %d]", x, y, tileX(), tileY());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Position position = (Position) obj;
        return Double.compare(position.x, x) == 0 && Double.compare(position.y, y) == 0;
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(x, y);
    }
}