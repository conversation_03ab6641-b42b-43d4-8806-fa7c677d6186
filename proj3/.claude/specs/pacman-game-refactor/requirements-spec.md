# Pac-Man Game Refactor - Technical Specifications

## Problem Statement
- **Business Issue**: Current codebase has monolithic Main class with hardcoded game logic, making it difficult to add new features like ghost AI, audio, smooth movement, and menu systems
- **Current State**: Single Main.java file handles all game logic including rendering, input, movement, and world generation with no separation of concerns
- **Expected Outcome**: Modular, extensible architecture supporting 9 feature areas with clean interfaces for future expansion

## Solution Overview
- **Approach**: Refactor monolithic architecture into modular component-based system with dedicated managers for each feature area
- **Core Changes**: Extract game loop, entity management, asset loading, state management, and rendering into separate systems with well-defined interfaces
- **Success Criteria**: Enable addition of all 9 feature areas without modifying core framework, maintain 60fps performance, support extensible design patterns

## Technical Implementation

### Database Changes
- **Save File Structure**: Create JSON-based save system in same directory as executable
  - `pacman_save.json` with game state, score, player position, world chunks
  - Migration: Convert current static world to chunk-based serializable format

### Code Changes

#### Core Architecture Files
- **Files to Modify**: 
  - `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/core/Main.java` - Refactor to use GameEngine
  - `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/core/World.java` - Extract to WorldManager
  - `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/tileengine/TERenderer.java` - Extend for smooth movement support

#### New Core System Files
- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/core/GameEngine.java`
  - **Purpose**: Main game loop coordinator and system orchestrator
  - **Key Methods**: `initialize()`, `gameLoop()`, `handleInput()`, `update()`, `render()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/core/GameState.java`
  - **Purpose**: Centralized state management (START_MENU, PLAYING, PAUSED, GAME_OVER, SETTINGS)
  - **Key Methods**: `setState()`, `getState()`, `isState()`, `canTransition()`

#### Entity System Files
- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/entities/Entity.java`
  - **Purpose**: Base class for all game entities with position and sprite management
  - **Key Fields**: `Position logicalPos`, `Position renderPos`, `Sprite sprite`, `MovementComponent movement`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/entities/Player.java`
  - **Purpose**: Player entity with input handling and skill management
  - **Key Methods**: `handleInput()`, `move()`, `activateSkill()`, `collectPellet()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/entities/Ghost.java`
  - **Purpose**: Base ghost class with AI behavior interface
  - **Key Methods**: `updateAI()`, `move()`, `setTarget()`, `getBehavior()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/entities/ghosts/`
  - **BlueGhost.java**: Circular patrol behavior
  - **PinkGhost.java**: Line-of-sight charge behavior  
  - **OrangeGhost.java**: Tracking within distance threshold
  - **RedGhost.java**: Pathfinding AI behavior

#### Component System Files
- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/components/Position.java`
  - **Purpose**: Enhanced position with sub-tile precision
  - **Key Fields**: `double x`, `double y`, `int tileX()`, `int tileY()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/components/MovementComponent.java`
  - **Purpose**: Smooth movement with velocity and interpolation
  - **Key Methods**: `setVelocity()`, `updatePosition()`, `isMoving()`, `canMove()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/components/SpriteComponent.java`
  - **Purpose**: Sprite management with directional animations
  - **Key Methods**: `setDirection()`, `updateAnimation()`, `getCurrentFrame()`

#### Manager System Files
- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/managers/EntityManager.java`
  - **Purpose**: Entity lifecycle management and collision detection
  - **Key Methods**: `addEntity()`, `removeEntity()`, `updateAll()`, `checkCollisions()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/managers/AssetManager.java`
  - **Purpose**: Sprite and audio asset loading with caching
  - **Key Methods**: `loadSprite()`, `loadAudio()`, `getAsset()`, `preloadAssets()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/managers/AudioManager.java`
  - **Purpose**: Audio playback with looping and volume control
  - **Key Methods**: `playMusic()`, `playSound()`, `setVolume()`, `stopAll()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/managers/WorldManager.java`
  - **Purpose**: Chunk-based world generation and infinite scrolling
  - **Key Methods**: `generateChunk()`, `loadChunk()`, `unloadChunk()`, `getVisibleChunks()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/managers/ScoreManager.java`
  - **Purpose**: Score tracking with pellet values and formatting
  - **Key Methods**: `addScore()`, `getFormattedScore()`, `saveHighScore()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/managers/SkillManager.java`
  - **Purpose**: Power pellet and skill system with extensible design
  - **Key Methods**: `activateSkill()`, `updateSkills()`, `registerSkill()`, `getActiveSkills()`

#### UI System Files
- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/ui/MenuSystem.java`
  - **Purpose**: Menu state management and navigation
  - **Key Methods**: `showStartMenu()`, `showGameOverMenu()`, `showSettings()`, `handleMenuInput()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/ui/HUD.java`
  - **Purpose**: In-game UI elements (score, skills, progress bars)
  - **Key Methods**: `renderScore()`, `renderSkillBar()`, `renderHealth()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/ui/ThemeManager.java`
  - **Purpose**: Color scheme management for Tokyo Night/Monokai/Groovebox
  - **Key Methods**: `setTheme()`, `getThemeColors()`, `applyTheme()`

#### Rendering System Files
- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/rendering/SmoothRenderer.java`
  - **Purpose**: Enhanced renderer supporting sub-tile positioning and camera
  - **Key Methods**: `renderEntity()`, `setCameraOffset()`, `renderWithInterpolation()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/rendering/CameraSystem.java`
  - **Purpose**: Camera management for infinite scrolling
  - **Key Methods**: `followEntity()`, `setOffset()`, `worldToScreen()`, `screenToWorld()`

#### AI System Files
- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/ai/AIBehavior.java`
  - **Purpose**: Interface for ghost AI behaviors
  - **Key Methods**: `update()`, `getNextMove()`, `setTarget()`, `canSeePlayer()`

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/ai/PathfindingAI.java`
  - **Purpose**: A* pathfinding implementation for Red Ghost
  - **Key Methods**: `findPath()`, `getNextStep()`, `isValidPath()`

#### Configuration Files
- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/config/GameConfig.java`
  - **Purpose**: Centralized configuration constants
  - **Key Fields**: Movement speeds, pellet values, skill durations, chunk sizes

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/config/AssetPaths.java`
  - **Purpose**: Asset file path constants
  - **Key Fields**: Sprite paths, audio paths, theme configurations

#### Data Files
- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/data/SaveData.java`
  - **Purpose**: Save game data structure
  - **Key Fields**: Player position, score, world chunks, settings

- **File**: `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/data/WorldChunk.java`
  - **Purpose**: Serializable world chunk data
  - **Key Methods**: `serialize()`, `deserialize()`, `generateTiles()`

### Asset Structure Changes
- **New Directories**:
  - `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/assets/sprites/ghosts/` - Ghost sprite sheets
  - `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/assets/sprites/walls/` - Wall variant sprites
  - `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/assets/sprites/ui/` - UI element sprites
  - `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/assets/audio/` - Music and sound effects
  - `/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/assets/themes/` - Color scheme definitions

### Configuration Changes
- **Game Constants**: Extract hardcoded values to GameConfig.java
  - Tile size: 16x16 pixels
  - Movement speeds: Blue=1.0, Pink=1.5, Orange=1.0, Red=1.2
  - Skill durations: Power pellet=3 seconds
  - Chunk size: 50x50 tiles
  - Score values: Pellet=1 point (configurable)

## Implementation Sequence

### Phase 1: Core Architecture Foundation
1. **Extract GameEngine from Main.java** - Move game loop and initialization logic
2. **Create GameState enum and manager** - Centralized state management system
3. **Implement Position component** - Sub-tile positioning support
4. **Create Entity base class** - Foundation for player and ghosts
5. **Extract EntityManager** - Entity lifecycle and collision detection

### Phase 2: Asset and Rendering Systems
1. **Implement AssetManager** - Sprite and audio loading with caching
2. **Create SpriteComponent** - Directional animation management
3. **Extend TERenderer to SmoothRenderer** - Sub-tile rendering support
4. **Implement CameraSystem** - Viewport management for scrolling
5. **Create ThemeManager** - Color scheme system

### Phase 3: Core Game Systems
1. **Implement MovementComponent** - Smooth movement with velocity
2. **Create Player entity** - Input handling and movement
3. **Implement ScoreManager** - Score tracking and formatting
4. **Create MenuSystem** - Start/game/settings/gameover states
5. **Implement AudioManager** - Music and sound effect playback

### Phase 4: Advanced Features
1. **Create Ghost base class and AI interface** - Ghost behavior foundation
2. **Implement specific ghost types** - Blue, Pink, Orange, Red behaviors
3. **Create SkillManager** - Power pellet and extensible skill system
4. **Implement WorldManager** - Chunk-based infinite scrolling
5. **Create save/load system** - Game state persistence

## Validation Plan

### Unit Tests
- **EntityManager**: Entity lifecycle, collision detection accuracy
- **MovementComponent**: Smooth interpolation, velocity calculations
- **AssetManager**: Asset loading, caching behavior, missing file handling
- **ScoreManager**: Score calculation, formatting (comma separation)
- **SkillManager**: Skill activation, duration tracking, stacking prevention
- **AIBehavior**: Ghost movement patterns, pathfinding accuracy
- **WorldManager**: Chunk generation, loading/unloading logic

### Integration Tests
- **Game State Transitions**: Menu navigation, pause/resume functionality
- **Entity Interactions**: Player-ghost collision, pellet collection
- **Audio System**: Background music looping, volume control
- **Theme System**: Color scheme application across all tiles
- **Save/Load System**: Game state persistence and restoration
- **Camera System**: Smooth scrolling, entity following

### Business Logic Verification
- **Ghost AI Behaviors**: 
  - Blue ghost circular patrol at avatar speed
  - Pink ghost line-of-sight charging until wall collision
  - Orange ghost tracking within distance threshold
  - Red ghost pathfinding with increased speed
- **Skill System**: Power pellet 3-second duration, laser shooting mechanics
- **Scoring System**: 1 point per pellet, comma formatting for display
- **Movement System**: Different entity speeds, directional sprite changes
- **Infinite Scrolling**: Smooth vertical world generation, chunk management

### Performance Requirements
- **Frame Rate**: Maintain stable 60fps during gameplay
- **Memory Usage**: Efficient chunk loading/unloading for infinite scrolling
- **Asset Loading**: Minimize loading times with asset caching
- **Collision Detection**: Efficient spatial partitioning for entity interactions

## Integration Points

### Existing Codebase Integration
- **TERenderer Extension**: Maintain compatibility while adding sub-tile rendering
- **TETile System**: Extend with sprite support and theme color application
- **World Generation**: Preserve existing room/hallway generation logic in chunks
- **Input Handling**: Maintain StdDraw keyboard input while adding smooth movement

### External Dependencies
- **StdDraw Library**: Continue using for rendering and input
- **Java Audio API**: For MP3/WAV playback support
- **JSON Library**: For save file serialization (minimal external dependency)

### Module Communication Patterns
- **Event System**: Observer pattern for entity interactions and state changes
- **Component System**: Entity-Component pattern for modular behavior
- **Manager Pattern**: Centralized systems for specific functionality areas
- **Factory Pattern**: Asset creation and ghost type instantiation

## Extension Framework

### Design Patterns for Future Features
- **Strategy Pattern**: Ghost AI behaviors for easy addition of new ghost types
- **Observer Pattern**: Event system for entity interactions and achievements
- **Factory Pattern**: Asset creation and entity instantiation
- **State Pattern**: Game state management and menu navigation
- **Component Pattern**: Modular entity behavior system

### Extensibility Points
- **New Ghost Types**: Implement AIBehavior interface with custom logic
- **Additional Skills**: Register with SkillManager using common interface
- **New Themes**: Add color schemes to ThemeManager configuration
- **World Generation**: Extend WorldManager with new chunk generation algorithms
- **Audio Effects**: Register sound events with AudioManager
- **Save Data**: Extend SaveData class with new persistent fields

### Configuration System
- **Runtime Configuration**: GameConfig.java for tweaking game parameters
- **Asset Configuration**: AssetPaths.java for easy asset management
- **Theme Configuration**: JSON-based theme definitions for easy customization
- **Skill Configuration**: Extensible skill registration system

This specification provides a complete blueprint for refactoring the monolithic Pac-Man game into a modular, extensible architecture that supports all 9 confirmed feature areas while maintaining clean code principles and enabling future expansion.