# Pacman Hub World - Technical Specifications

## Problem Statement
- **Business Issue**: Current game only supports single-level play with no progression system
- **Current State**: Game starts directly in PLAYING state with one world, lacking level variety and progression
- **Expected Outcome**: Hub world system where players navigate between multiple levels, unlock content progressively, and have clear visual indicators of completion status

## Solution Overview
- **Approach**: Extend existing World.java generation system to create a hub world with designated level rooms, add Level class for game mode management, integrate through GameState.HUB_WORLD
- **Core Changes**: New HubWorld class extending World, Level class with game modes, GameState.HUB_WORLD integration, door interaction system
- **Success Criteria**: Players can navigate hub world, enter/exit levels seamlessly, see visual progression feedback, and levels unlock in sequence

## Technical Implementation

### Database Changes
- **Tables to Modify**: None - using existing SaveData.java structure
- **New Fields in SaveData.java**:
  - `Map<Integer, Boolean> unlockedLevels` - Track which levels are unlocked
  - `Map<Integer, LevelCompletionData> levelProgress` - Store completion data per level
  - `String currentWorldType` - Track if in hub or level ("HUB", "LEVEL")
  - `Integer currentLevelId` - Current level ID when in level play

### Code Changes

#### Files to Modify
1. **`/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/core/GameState.java`**
   - Add `HUB_WORLD("Hub World")` and `LEVEL_PLAYING("Level Playing")` states
   - Update `canTransition()` method to handle new state flow
   - Update helper methods to support hub navigation

2. **`/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/tileengine/Tileset.java`** 
   - Add `LEVEL_DOOR_LOCKED` and `LEVEL_DOOR_UNLOCKED` tiles for visual feedback

#### New Files to Create
1. **`/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/core/HubWorld.java`**
   - Extends World class
   - Designates level rooms and adds door tiles
   - Handles avatar-door interaction detection

2. **`/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/core/Level.java`**
   - Manages individual level instances
   - Contains game mode logic (Survive/Eat/Score)
   - Tracks completion criteria and difficulty scaling

3. **`/Users/<USER>/Documents/cs61bl/project-3-byow-pacman/proj3/src/managers/LevelManager.java`**
   - Manages level progression and unlocking
   - Handles transitions between hub and levels
   - Integrates with SaveData for persistence

### API Changes
- **Door Interaction System**: Method signatures for detecting avatar-door proximity and key press handling
- **Level Transition API**: Methods for entering/exiting levels and maintaining game state
- **Progress Tracking**: APIs for updating level completion status and unlock progression

### Configuration Changes
- **Level Configuration**: Constants for number of hub rooms (5-8), door positions, and unlock sequence
- **Game Mode Settings**: Difficulty scaling parameters (ghost count, power pellet count per level)
- **Input Bindings**: Key mappings for door interaction ('E' or 'SPACE')

## Implementation Sequence

### Phase 1: Core Infrastructure
**Files**: `GameState.java`, `Tileset.java`
- Add HUB_WORLD and LEVEL_PLAYING to GameState enum
- Update canTransition() method with new state flow logic
- Add door tile definitions to Tileset
- Update state helper methods (isGameplayActive, shouldRenderWorld)

### Phase 2: Hub World System
**Files**: `HubWorld.java`, `LevelManager.java` (skeleton)
- Create HubWorld class extending World
- Implement room designation system (mark 5-8 rooms as level rooms)
- Add door tile placement in designated rooms
- Create basic LevelManager skeleton for coordination

### Phase 3: Level System
**Files**: `Level.java`, complete `LevelManager.java`
- Implement Level class with game modes (Survive/Eat/Score)
- Add difficulty scaling logic (more ghosts, fewer power pellets)
- Complete LevelManager with progression and persistence logic
- Integrate with SaveData for progress tracking

### Phase 4: Interaction and Integration
**Files**: Player input handling, game engine integration
- Implement door proximity detection and key press handling
- Add level entry/exit transition logic
- Integrate hub/level switching with existing game systems
- Add visual feedback for level completion status

## Validation Plan

### Unit Tests
- **HubWorld Generation**: Verify correct room designation and door placement
- **Level Progression**: Test unlock sequence and completion tracking  
- **Game State Transitions**: Validate state flow START_MENU → HUB_WORLD → LEVEL_PLAYING → HUB_WORLD
- **Save/Load Integration**: Test persistence of hub progress and level completion

### Integration Tests
- **Door Interaction**: Test avatar proximity detection and key press response
- **Level Entry/Exit**: Verify seamless transitions maintain proper game state
- **Visual Feedback**: Confirm door tiles update correctly based on unlock status
- **Progression Chain**: Test that completing Level N unlocks Level N+1

### Business Logic Verification
- **Hub Navigation**: Players can move freely in hub world using existing controls
- **Level Variety**: Different game modes provide distinct gameplay experiences
- **Progress Persistence**: Level completion and unlock status survive save/load cycles
- **Visual Clarity**: Players can clearly distinguish locked vs unlocked levels

---

## Detailed Class Specifications

### HubWorld.java
```java
package core;

/**
 * Hub world implementation for level selection and navigation.
 * Extends World to reuse generation logic while adding level-specific functionality.
 * 
 * This class provides:
 * - Room-based world generation using existing World.generateWorld()
 * - Designation of specific rooms as level entrance points
 * - Door tile placement and visual feedback system
 * - Integration with level unlocking progression
 */
public class HubWorld extends World {
    // Hub configuration
    private static final int MIN_LEVEL_ROOMS = 5;
    private static final int MAX_LEVEL_ROOMS = 8;
    private static final int DOOR_PLACEMENT_ATTEMPTS = 10;
    
    // Level room management
    private List<Room> levelRooms;           // Rooms designated for level entrances
    private Map<Integer, Room> levelRoomMap; // Maps level ID to room
    private Map<Room, Position> doorPositions; // Door positions within rooms
    
    // Hub state
    private LevelManager levelManager;
    private Position playerSpawnPosition;
    
    // TODO: Constructor - Initialize HubWorld with seed and level manager
    // TODO: generateHubWorld() - Generate world and designate level rooms
    // TODO: designateLevelRooms() - Select rooms for level entrances
    // TODO: placeDoorTiles() - Add door tiles to designated rooms
    // TODO: updateDoorTiles() - Update door appearance based on unlock status
    // TODO: checkDoorInteraction() - Detect player proximity to doors
    // TODO: getDoorAtPosition() - Get level ID for door at position
    // TODO: isValidDoorPosition() - Check if position is suitable for door
}
```

### Level.java
```java
package core;

/**
 * Individual level instance with specific game mode and difficulty.
 * Manages level-specific logic, completion criteria, and scoring.
 * 
 * This class provides:
 * - Game mode implementation (Survive/Eat Pellets/Reach Score)
 * - Difficulty scaling based on level progression
 * - Completion detection and progress tracking
 * - Integration with scoring and ghost management systems
 */
public class Level {
    // Level identification
    private int levelId;
    private String levelName;
    private GameMode gameMode;
    private DifficultyLevel difficulty;
    
    // Game mode settings
    private double surviveTimeTarget;    // For SURVIVE mode
    private int pelletsToEat;           // For EAT_PELLETS mode  
    private int targetScore;            // For SCORE_TARGET mode
    
    // Difficulty scaling
    private int ghostCount;             // Number of ghosts to spawn
    private int powerPelletCount;       // Number of power pellets
    private double ghostSpeedMultiplier; // Ghost movement speed modifier
    
    // Level state
    private boolean isCompleted;
    private boolean isUnlocked;
    private double levelTime;           // Time spent in level
    private LevelCompletionData completionData;
    
    // Game mode definitions
    public enum GameMode {
        SURVIVE,      // Survive for X seconds
        EAT_PELLETS,  // Eat all pellets in level
        SCORE_TARGET  // Reach target score
    }
    
    public enum DifficultyLevel {
        EASY, MEDIUM, HARD, EXPERT
    }
    
    // TODO: Constructor - Initialize level with ID, mode, and difficulty
    // TODO: generateLevelWorld() - Create level-specific world layout
    // TODO: checkCompletionCriteria() - Evaluate if level is complete
    // TODO: calculateDifficultyScaling() - Set ghost count and power pellets
    // TODO: startLevel() - Initialize level state and entities
    // TODO: updateLevel() - Update level-specific logic during gameplay
    // TODO: completeLevel() - Handle level completion and data recording
    // TODO: resetLevel() - Reset level state for retry
}
```

### LevelManager.java
```java
package managers;

/**
 * Central manager for level progression, unlocking, and hub integration.
 * Coordinates between hub world, individual levels, and save system.
 * 
 * This class provides:
 * - Level progression and unlocking logic
 * - Transitions between hub and level gameplay
 * - Integration with SaveData for progress persistence
 * - Level generation and configuration management
 */
public class LevelManager {
    // Level configuration
    private static final int TOTAL_LEVELS = 8;
    private Map<Integer, Level> levels;
    private Set<Integer> unlockedLevels;
    
    // Current state
    private Level currentLevel;
    private HubWorld hubWorld;
    private GameState previousState;
    
    // Progression system
    private Map<Integer, LevelCompletionData> completionData;
    private boolean hubWorldGenerated;
    
    // Integration points
    private SaveData saveData;
    private ScoreManager scoreManager;
    
    // TODO: Constructor - Initialize level manager with save data integration
    // TODO: initializeLevels() - Create all level instances with configurations
    // TODO: loadProgressFromSave() - Restore level progress from SaveData
    // TODO: saveProgressToSave() - Persist current progress to SaveData
    // TODO: unlockLevel() - Mark level as available and update visuals
    // TODO: enterLevel() - Transition from hub to specific level
    // TODO: exitLevel() - Return to hub from level (completion or quit)
    // TODO: checkLevelCompletion() - Evaluate completion and unlock next level
    // TODO: generateHubWorld() - Create hub world with current unlock status
    // TODO: updateDoorVisuals() - Refresh door tile appearances in hub
    // TODO: getUnlockedLevels() - Return set of available level IDs
    // TODO: getCurrentLevel() - Get active level instance
    // TODO: getLevelProgress() - Get completion data for specific level
}
```

### GameState.java Updates
```java
// Additional enum values to add:
HUB_WORLD("Hub World"),
LEVEL_PLAYING("Level Playing"),

// Updated canTransition() method logic:
// START_MENU -> HUB_WORLD, SETTINGS
// HUB_WORLD -> LEVEL_PLAYING, PAUSED, START_MENU  
// LEVEL_PLAYING -> HUB_WORLD, PAUSED, GAME_OVER
// PAUSED -> (previous state: HUB_WORLD or LEVEL_PLAYING)
```

### SaveData.java Extensions
```java
// Additional fields for hub world support:
private Map<Integer, Boolean> unlockedLevels;
private Map<Integer, LevelCompletionData> levelProgress;
private String currentWorldType; // "HUB" or "LEVEL"
private Integer currentLevelId;

// TODO: Hub-specific save/load methods
// TODO: Level progress serialization
// TODO: Hub world state persistence
```

This specification provides comprehensive implementation guidance for creating skeleton code with detailed TODO comments, matching the style demonstrated in CircularPatrolAI.java while integrating seamlessly with the existing codebase architecture.