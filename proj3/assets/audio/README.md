# Audio Assets

This directory should contain the following audio files for the Pac-Man game:

## Background Music
- `background_music.wav` - Main game background music (looping)
- `menu_music.wav` - Menu background music (looping)

## Sound Effects
- `pellet_eat.wav` - Sound when eating regular pellets
- `power_pellet_eat.wav` - Sound when eating power pellets
- `ghost_eat.wav` - Sound when eating ghosts (during power mode)
- `player_death.wav` - Sound when player dies
- `level_complete.wav` - Sound when completing a level
- `game_over.wav` - Game over sound

## UI Sounds
- `menu_select.wav` - Menu selection/navigation sound
- `menu_confirm.wav` - Menu confirmation sound
- `button_click.wav` - Button click sound

## Audio Format Requirements
- **Format**: WAV, MP3, or OGG
- **Sample Rate**: 44.1 kHz recommended
- **Bit Depth**: 16-bit recommended
- **Channels**: Mono or Stereo

## Audio Aliases
The AudioManager provides the following aliases for easier reference:
- `"background"` → `background_music.wav`
- `"menu"` → `menu_music.wav`
- `"pellet"` → `pellet_eat.wav`
- `"power_pellet"` → `power_pellet_eat.wav`
- `"ghost_eat"` → `ghost_eat.wav`
- `"death"` → `player_death.wav`
- `"level_complete"` → `level_complete.wav`
- `"game_over"` → `game_over.wav`
- `"menu_select"` → `menu_select.wav`
- `"menu_confirm"` → `menu_confirm.wav`
- `"button_click"` → `button_click.wav`

## Usage Example
```java
// Play background music
audioManager.playMusic("background", true);

// Play sound effect
audioManager.playSound("pellet", 1.0, false);

// Use actual filename instead of alias
audioManager.playSound("custom_sound.wav", 0.8, false);
```

## Notes
- Audio files are loaded on-demand through the AssetManager
- The AudioManager supports volume control per category (MUSIC, SOUND_EFFECTS, UI_SOUNDS, VOICE)
- All audio can be muted/unmuted and paused/resumed globally
- Sound effects can be played with custom volume levels and looping
